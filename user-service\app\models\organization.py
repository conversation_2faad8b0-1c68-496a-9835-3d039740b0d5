import uuid
from datetime import datetime
from sqlalchemy import Column, ForeignKey, String, DateTime, JSON
from sqlalchemy.orm import relationship
from app.models.user import Base

class Organization(Base):
    __tablename__ = "organizations"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    title = Column(String, nullable=False, index=True)
    description = Column(String, nullable=True)
    owner_id = Column(String, ForeignKey("users.id"), nullable=False) # Link to the User table
    user_ids = Column(JSON, nullable=False, default=list) # Store list of member user IDs

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship to the owner (User)
    owner = relationship("User", backref="owned_organizations")

    def __repr__(self):
        return f"<Organization {self.title} (Owner: {self.owner_id})>"

# Optional: Add a relationship to User model if needed (e.g., organizations they belong to)
# In models/user.py, inside the User class:
# owned_organizations = relationship("Organization", back_populates="owner")
# Note: managing membership via the JSON field doesn't automatically create a backref
# for all members easily. Querying membership involves checking the user_ids JSON field.