﻿{"AI":{"AgenticAI":{"name":"AgenticAI","display_name":"AI Agent Executor","description":"Executes an AI agent with tools and memory using AutoGen.","category":"AI","icon":"Bot","beta":true,"requires_approval":false,"inputs":[{"name":"model_provider","display_name":"Model Provider","info":"The AI model provider to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"OpenAI","options":["OpenAI","Azure OpenAI","Anthropic","Claude","Google","Gemini","Mistral","Ollama","Custom"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"base_url","display_name":"Base URL","info":"Base URL for the API (leave empty for default provider URL).","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"model_provider","field_value":"Custom","operator":"equals"},{"field_name":"model_provider","field_value":"Azure OpenAI","operator":"equals"},{"field_name":"model_provider","field_value":"Ollama","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"api_key","display_name":"API Key","info":"API key for the model provider. Can be entered directly or referenced from secure storage.","input_type":"credential","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR","credential_type":"api_key","use_credential_id":false,"credential_id":""},{"name":"model_name","display_name":"Model","info":"Select the model to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"temperature","display_name":"Temperature","info":"Controls randomness: 0 is deterministic, higher values are more random.","input_type":"float","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":0.7,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"objective","display_name":"Objective","info":"The task or objective for the agent to accomplish. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_variables","display_name":"Input Variables","info":"Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"tools","display_name":"Tools","info":"List of tools available to the agent. Can be connected from another node or entered directly.","input_type":"list","input_types":["list","Any"],"required":false,"is_handle":true,"is_list":true,"real_time_refresh":false,"advanced":false,"value":[],"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"memory","display_name":"Memory Object","info":"Connect a memory object from another node.","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"agent_type","display_name":"Agent Type","info":"The type of agent to create.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"Assistant","options":["Assistant","UserProxy","CodeExecutor"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"stream","display_name":"Stream Response","info":"Enable streaming for real-time responses.","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":false,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"final_answer","display_name":"Final Answer","output_type":"string","method":null},{"name":"intermediate_steps","display_name":"Intermediate Steps","output_type":"list","method":null},{"name":"updated_memory","display_name":"Updated Memory","output_type":"Memory","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.ai.agenticai","interface_issues":[]},"BasicLLMChain":{"name":"BasicLLMChain","display_name":"LLM Chain (Basic)","description":"Runs a simple LLMChain with a prompt template.","category":"AI","icon":"Chain","beta":false,"requires_approval":false,"inputs":[{"name":"model_provider","display_name":"Model Provider","info":"The AI model provider to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"OpenAI","options":["OpenAI","Azure OpenAI","Anthropic","Claude","Google","Gemini","Mistral","Ollama","Custom"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"base_url","display_name":"Base URL","info":"Base URL for the API (leave empty for default provider URL).","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"model_provider","field_value":"Custom","operator":"equals"},{"field_name":"model_provider","field_value":"Azure OpenAI","operator":"equals"},{"field_name":"model_provider","field_value":"Ollama","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"api_key","display_name":"API Key","info":"API key for the model provider. Can be entered directly or referenced from secure storage.","input_type":"credential","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR","credential_type":"api_key","use_credential_id":false,"credential_id":""},{"name":"model_name","display_name":"Model","info":"Select the model to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"llm_handle","display_name":"LLM Object","info":"Connect a language model object from another node.","input_type":"handle","input_types":["Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"prompt_template_handle","display_name":"Prompt Template","info":"Connect a prompt template object from another node.","input_type":"handle","input_types":["Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_variables_handle","display_name":"Input Variables","info":"Connect a dictionary of variables from another node.","input_type":"handle","input_types":["dict"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_variables","display_name":"Input Variables (Direct)","info":"Dictionary of variables to fill the prompt template. Used if no connection is provided.","input_type":"dict","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"output_text","display_name":"Generated Text","output_type":"string","method":null},{"name":"full_response","display_name":"Full Chain Response","output_type":"dict","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.ai.basicllmchain","interface_issues":[]},"Classifier":{"name":"Classifier","display_name":"Text Classifier","description":"Categorizes text into one of several predefined classes or labels.","category":"AI","icon":"Tag","beta":false,"requires_approval":false,"inputs":[{"name":"model_provider","display_name":"Model Provider","info":"The AI model provider to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"OpenAI","options":["OpenAI","Azure OpenAI","Anthropic","Claude","Google","Gemini","Mistral","Ollama","Custom"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"base_url","display_name":"Base URL","info":"Base URL for the API (leave empty for default provider URL).","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"model_provider","field_value":"Custom","operator":"equals"},{"field_name":"model_provider","field_value":"Azure OpenAI","operator":"equals"},{"field_name":"model_provider","field_value":"Ollama","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"api_key","display_name":"API Key","info":"API key for the model provider. Can be entered directly or referenced from secure storage.","input_type":"credential","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR","credential_type":"api_key","use_credential_id":false,"credential_id":""},{"name":"model_name","display_name":"Model","info":"Select the model to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"text_handle","display_name":"Text to Classify","info":"Connect the text that needs to be assigned a category.","input_type":"handle","input_types":["string","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"text","display_name":"Text to Classify (Direct)","info":"The text that needs to be assigned a category. Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"categories_handle","display_name":"Categories","info":"Connect a list of possible categories from another node.","input_type":"handle","input_types":["list","string"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"categories","display_name":"Categories (Direct)","info":"The set of valid categories the text can be assigned to. Provide as a list or comma-separated string. Used if no connection is provided.","input_type":"list","input_types":null,"required":false,"is_handle":false,"is_list":true,"real_time_refresh":false,"advanced":false,"value":[],"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"category","display_name":"Assigned Category","output_type":"string","method":null},{"name":"confidence","display_name":"Confidence","output_type":"float","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"app.components.ai.classifier","interface_issues":[]},"InformationExtractor":{"name":"InformationExtractor","display_name":"Information Extractor","description":"Extracts specific information from text based on a query.","category":"AI","icon":"FileSearch","beta":false,"requires_approval":false,"inputs":[{"name":"model_provider","display_name":"Model Provider","info":"The AI model provider to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"OpenAI","options":["OpenAI","Azure OpenAI","Anthropic","Claude","Google","Gemini","Mistral","Ollama","Custom"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"base_url","display_name":"Base URL","info":"Base URL for the API (leave empty for default provider URL).","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"model_provider","field_value":"Custom","operator":"equals"},{"field_name":"model_provider","field_value":"Azure OpenAI","operator":"equals"},{"field_name":"model_provider","field_value":"Ollama","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"api_key","display_name":"API Key","info":"API key for the model provider. Can be entered directly or referenced from secure storage.","input_type":"credential","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR","credential_type":"api_key","use_credential_id":false,"credential_id":""},{"name":"model_name","display_name":"Model","info":"Select the model to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"text_handle","display_name":"Source Text","info":"Connect the text from which to extract information.","input_type":"handle","input_types":["string","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"text","display_name":"Source Text (Direct)","info":"The text from which to extract information. Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"query_handle","display_name":"Extraction Query","info":"Connect the query specifying what information to extract.","input_type":"handle","input_types":["string"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"query","display_name":"Extraction Query (Direct)","info":"Specifies what information to extract (e.g., 'Extract email addresses', 'Find the customer name'). Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"extracted_info","display_name":"Extracted Information","output_type":"string","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"app.components.ai.informationextractor","interface_issues":[]},"OpenAIModule":{"name":"OpenAIModule","display_name":"OpenAI Call","description":"Direct OpenAI API call.","category":"AI","icon":"OpenAI","beta":false,"requires_approval":false,"inputs":[{"name":"model_provider","display_name":"Model Provider","info":"The AI model provider to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"OpenAI","options":["OpenAI","Azure OpenAI","Anthropic","Claude","Google","Gemini","Mistral","Ollama","Custom"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"base_url","display_name":"Base URL","info":"Base URL for the API (leave empty for default provider URL).","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"model_provider","field_value":"Custom","operator":"equals"},{"field_name":"model_provider","field_value":"Azure OpenAI","operator":"equals"},{"field_name":"model_provider","field_value":"Ollama","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"api_key","display_name":"API Key","info":"API key for the model provider. Can be entered directly or referenced from secure storage.","input_type":"credential","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR","credential_type":"api_key","use_credential_id":false,"credential_id":""},{"name":"model_name","display_name":"Model","info":"Select the model to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"endpoint","display_name":"API Endpoint","info":"The OpenAI API endpoint to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"ChatCompletion","options":["ChatCompletion","Completion (Legacy)"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"messages_handle","display_name":"Messages","info":"Connect a list of message objects from another node.","input_type":"handle","input_types":["list"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"endpoint","field_value":"ChatCompletion","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"messages","display_name":"Messages (Direct)","info":"List of message objects for ChatCompletion. Each message should have 'role' and 'content' keys. Used if no connection is provided.","input_type":"list","input_types":null,"required":false,"is_handle":false,"is_list":true,"real_time_refresh":false,"advanced":false,"value":[],"options":null,"visibility_rules":[{"field_name":"endpoint","field_value":"ChatCompletion","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"prompt_handle","display_name":"Prompt","info":"Connect a text prompt from another node.","input_type":"handle","input_types":["string"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"endpoint","field_value":"Completion (Legacy)","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"prompt","display_name":"Prompt (Direct)","info":"Text prompt for Completion API. Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"endpoint","field_value":"Completion (Legacy)","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"max_tokens","display_name":"Max Tokens","info":"Maximum number of tokens to generate.","input_type":"int","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":1000,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"temperature","display_name":"Temperature","info":"Controls randomness: 0 is deterministic, higher values are more random.","input_type":"float","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":0.7,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"response","display_name":"API Response","output_type":"dict","method":null},{"name":"content","display_name":"Generated Content","output_type":"string","method":null},{"name":"usage","display_name":"Token Usage","output_type":"dict","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.ai.openaimodule","interface_issues":[]},"QuestionAnswerModule":{"name":"QuestionAnswerModule","display_name":"Question Answering","description":"Answers questions based on a provided context document.","category":"AI","icon":"HelpCircle","beta":false,"requires_approval":false,"inputs":[{"name":"model_provider","display_name":"Model Provider","info":"The AI model provider to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"OpenAI","options":["OpenAI","Azure OpenAI","Anthropic","Claude","Google","Gemini","Mistral","Ollama","Custom"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"base_url","display_name":"Base URL","info":"Base URL for the API (leave empty for default provider URL).","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"model_provider","field_value":"Custom","operator":"equals"},{"field_name":"model_provider","field_value":"Azure OpenAI","operator":"equals"},{"field_name":"model_provider","field_value":"Ollama","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"api_key","display_name":"API Key","info":"API key for the model provider. Can be entered directly or referenced from secure storage.","input_type":"credential","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR","credential_type":"api_key","use_credential_id":false,"credential_id":""},{"name":"model_name","display_name":"Model","info":"Select the model to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"question_handle","display_name":"Question","info":"Connect the question to be answered.","input_type":"handle","input_types":["string"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"question","display_name":"Question (Direct)","info":"The question to be answered. Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"context_handle","display_name":"Context Document","info":"Connect the context document containing the information needed to answer the question.","input_type":"handle","input_types":["string","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"context","display_name":"Context Document (Direct)","info":"The text containing the information needed to answer the question. Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"answer","display_name":"Answer","output_type":"string","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"app.components.ai.questionanswermodule","interface_issues":[]},"SentimentAnalyzer":{"name":"SentimentAnalyzer","display_name":"Sentiment Analyzer","description":"Analyzes text to determine its sentiment (Positive, Negative, Neutral).","category":"AI","icon":"BarChart","beta":false,"requires_approval":false,"inputs":[{"name":"model_provider","display_name":"Model Provider","info":"The AI model provider to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"OpenAI","options":["OpenAI","Azure OpenAI","Anthropic","Claude","Google","Gemini","Mistral","Ollama","Custom"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"base_url","display_name":"Base URL","info":"Base URL for the API (leave empty for default provider URL).","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"model_provider","field_value":"Custom","operator":"equals"},{"field_name":"model_provider","field_value":"Azure OpenAI","operator":"equals"},{"field_name":"model_provider","field_value":"Ollama","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"api_key","display_name":"API Key","info":"API key for the model provider. Can be entered directly or referenced from secure storage.","input_type":"credential","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR","credential_type":"api_key","use_credential_id":false,"credential_id":""},{"name":"model_name","display_name":"Model","info":"Select the model to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"text_handle","display_name":"Text to Analyze","info":"Connect the text whose sentiment needs to be evaluated.","input_type":"handle","input_types":["string","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"text","display_name":"Text to Analyze (Direct)","info":"The text whose sentiment needs to be evaluated. Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"sentiment","display_name":"Sentiment","output_type":"string","method":null},{"name":"confidence","display_name":"Confidence","output_type":"float","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"app.components.ai.sentimentanalyzer","interface_issues":[]},"Summarizer":{"name":"Summarizer","display_name":"Text Summarizer","description":"Creates a concise summary of a longer piece of text.","category":"AI","icon":"FileText","beta":false,"requires_approval":false,"inputs":[{"name":"model_provider","display_name":"Model Provider","info":"The AI model provider to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"OpenAI","options":["OpenAI","Azure OpenAI","Anthropic","Claude","Google","Gemini","Mistral","Ollama","Custom"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"base_url","display_name":"Base URL","info":"Base URL for the API (leave empty for default provider URL).","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"model_provider","field_value":"Custom","operator":"equals"},{"field_name":"model_provider","field_value":"Azure OpenAI","operator":"equals"},{"field_name":"model_provider","field_value":"Ollama","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"api_key","display_name":"API Key","info":"API key for the model provider. Can be entered directly or referenced from secure storage.","input_type":"credential","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR","credential_type":"api_key","use_credential_id":false,"credential_id":""},{"name":"model_name","display_name":"Model","info":"Select the model to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"text_handle","display_name":"Text to Summarize","info":"Connect the text that needs to be condensed.","input_type":"handle","input_types":["string","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"text","display_name":"Text to Summarize (Direct)","info":"The full text that needs to be condensed. Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"max_length","display_name":"Max Summary Length","info":"Maximum length of the summary in words. Use 0 for automatic length.","input_type":"int","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":200,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"summary","display_name":"Summary","output_type":"string","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"app.components.ai.summarizer","interface_issues":[]}},"Logic":{"ConditionalNode":{"name":"ConditionalNode","display_name":"Switch-Case Router","description":"Evaluates multiple conditions and routes data to matching outputs","category":"Logic","icon":"GitBranch","beta":false,"requires_approval":false,"inputs":[{"name":"primary_input_data","display_name":"Primary Input Data","info":"Main data to route through conditions. Can be connected from another node or entered directly.","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_1_source","display_name":"Condition 1 - Source","info":"Source of data for condition evaluation","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"node_output","options":["node_output","global_context"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_1_variable","display_name":"Condition 1 - Variable Name","info":"Global context variable name (only for global_context source)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"condition_1_source","field_value":"global_context","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_1_operator","display_name":"Condition 1 - Operator","info":"Comparison operator to apply","input_type":"dropdown","input_types":null,"required":true,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"equals","options":["equals","not_equals","contains","starts_with","ends_with","greater_than","less_than","exists","is_empty"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_1_expected_value","display_name":"Condition 1 - Expected Value","info":"Value to compare against (not used for exists/is_empty operators)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"condition_1_operator","field_value":"exists","operator":"not_equals"},{"field_name":"condition_1_operator","field_value":"is_empty","operator":"not_equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_1_use_primary","display_name":"Condition 1 - Use Primary Input Data","info":"Route primary input data when this condition matches","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_1_custom_input","display_name":"Condition 1 - Custom Input","info":"Custom data to route when condition matches (only if not using primary input)","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"condition_1_use_primary","field_value":"False","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_2_source","display_name":"Condition 2 - Source","info":"Source of data for condition evaluation","input_type":"dropdown","input_types":null,"required":true,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"node_output","options":["node_output","global_context"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_2_variable","display_name":"Condition 2 - Variable Name","info":"Global context variable name (only for global_context source)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"condition_2_source","field_value":"global_context","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_2_operator","display_name":"Condition 2 - Operator","info":"Comparison operator to apply","input_type":"dropdown","input_types":null,"required":true,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"equals","options":["equals","not_equals","contains","starts_with","ends_with","greater_than","less_than","exists","is_empty"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_2_expected_value","display_name":"Condition 2 - Expected Value","info":"Value to compare against (not used for exists/is_empty operators)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"condition_2_operator","field_value":"exists","operator":"not_equals"},{"field_name":"condition_2_operator","field_value":"is_empty","operator":"not_equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_2_use_primary","display_name":"Condition 2 - Use Primary Input Data","info":"Route primary input data when this condition matches","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_2_custom_input","display_name":"Condition 2 - Custom Input","info":"Custom data to route when condition matches (only if not using primary input)","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"condition_2_use_primary","field_value":"False","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"num_additional_conditions","display_name":"Number of Additional Conditions","info":"Number of additional conditions beyond the base 2 conditions (0-8). Total conditions will be 2 + this value.","input_type":"int","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":0,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_3_source","display_name":"Condition 3 - Source","info":"Source of data for condition evaluation","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"node_output","options":["node_output","global_context"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"1","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_3_variable","display_name":"Condition 3 - Variable Name","info":"Global context variable name (only for global_context source)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"1","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_3_source","field_value":"global_context","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_3_operator","display_name":"Condition 3 - Operator","info":"Comparison operator to apply","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"equals","options":["equals","not_equals","contains","starts_with","ends_with","greater_than","less_than","exists","is_empty"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"1","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_3_expected_value","display_name":"Condition 3 - Expected Value","info":"Value to compare against (not used for exists/is_empty operators)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"1","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_3_operator","field_value":"exists","operator":"not_equals"},{"field_name":"condition_3_operator","field_value":"is_empty","operator":"not_equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_3_use_primary","display_name":"Condition 3 - Use Primary Input Data","info":"Route primary input data when this condition matches","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"1","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_3_custom_input","display_name":"Condition 3 - Custom Input","info":"Custom data to route when condition matches (only if not using primary input)","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"1","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_3_use_primary","field_value":"False","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_4_source","display_name":"Condition 4 - Source","info":"Source of data for condition evaluation","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"node_output","options":["node_output","global_context"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_4_variable","display_name":"Condition 4 - Variable Name","info":"Global context variable name (only for global_context source)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_4_source","field_value":"global_context","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_4_operator","display_name":"Condition 4 - Operator","info":"Comparison operator to apply","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"equals","options":["equals","not_equals","contains","starts_with","ends_with","greater_than","less_than","exists","is_empty"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_4_expected_value","display_name":"Condition 4 - Expected Value","info":"Value to compare against (not used for exists/is_empty operators)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_4_operator","field_value":"exists","operator":"not_equals"},{"field_name":"condition_4_operator","field_value":"is_empty","operator":"not_equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_4_use_primary","display_name":"Condition 4 - Use Primary Input Data","info":"Route primary input data when this condition matches","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_4_custom_input","display_name":"Condition 4 - Custom Input","info":"Custom data to route when condition matches (only if not using primary input)","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_4_use_primary","field_value":"False","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_5_source","display_name":"Condition 5 - Source","info":"Source of data for condition evaluation","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"node_output","options":["node_output","global_context"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_5_variable","display_name":"Condition 5 - Variable Name","info":"Global context variable name (only for global_context source)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_5_source","field_value":"global_context","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_5_operator","display_name":"Condition 5 - Operator","info":"Comparison operator to apply","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"equals","options":["equals","not_equals","contains","starts_with","ends_with","greater_than","less_than","exists","is_empty"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_5_expected_value","display_name":"Condition 5 - Expected Value","info":"Value to compare against (not used for exists/is_empty operators)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_5_operator","field_value":"exists","operator":"not_equals"},{"field_name":"condition_5_operator","field_value":"is_empty","operator":"not_equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_5_use_primary","display_name":"Condition 5 - Use Primary Input Data","info":"Route primary input data when this condition matches","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_5_custom_input","display_name":"Condition 5 - Custom Input","info":"Custom data to route when condition matches (only if not using primary input)","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_5_use_primary","field_value":"False","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_6_source","display_name":"Condition 6 - Source","info":"Source of data for condition evaluation","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"node_output","options":["node_output","global_context"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_6_variable","display_name":"Condition 6 - Variable Name","info":"Global context variable name (only for global_context source)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_6_source","field_value":"global_context","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_6_operator","display_name":"Condition 6 - Operator","info":"Comparison operator to apply","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"equals","options":["equals","not_equals","contains","starts_with","ends_with","greater_than","less_than","exists","is_empty"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_6_expected_value","display_name":"Condition 6 - Expected Value","info":"Value to compare against (not used for exists/is_empty operators)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_6_operator","field_value":"exists","operator":"not_equals"},{"field_name":"condition_6_operator","field_value":"is_empty","operator":"not_equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_6_use_primary","display_name":"Condition 6 - Use Primary Input Data","info":"Route primary input data when this condition matches","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_6_custom_input","display_name":"Condition 6 - Custom Input","info":"Custom data to route when condition matches (only if not using primary input)","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_6_use_primary","field_value":"False","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_7_source","display_name":"Condition 7 - Source","info":"Source of data for condition evaluation","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"node_output","options":["node_output","global_context"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_7_variable","display_name":"Condition 7 - Variable Name","info":"Global context variable name (only for global_context source)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_7_source","field_value":"global_context","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_7_operator","display_name":"Condition 7 - Operator","info":"Comparison operator to apply","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"equals","options":["equals","not_equals","contains","starts_with","ends_with","greater_than","less_than","exists","is_empty"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_7_expected_value","display_name":"Condition 7 - Expected Value","info":"Value to compare against (not used for exists/is_empty operators)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_7_operator","field_value":"exists","operator":"not_equals"},{"field_name":"condition_7_operator","field_value":"is_empty","operator":"not_equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_7_use_primary","display_name":"Condition 7 - Use Primary Input Data","info":"Route primary input data when this condition matches","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_7_custom_input","display_name":"Condition 7 - Custom Input","info":"Custom data to route when condition matches (only if not using primary input)","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_7_use_primary","field_value":"False","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_8_source","display_name":"Condition 8 - Source","info":"Source of data for condition evaluation","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"node_output","options":["node_output","global_context"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_8_variable","display_name":"Condition 8 - Variable Name","info":"Global context variable name (only for global_context source)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_8_source","field_value":"global_context","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_8_operator","display_name":"Condition 8 - Operator","info":"Comparison operator to apply","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"equals","options":["equals","not_equals","contains","starts_with","ends_with","greater_than","less_than","exists","is_empty"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_8_expected_value","display_name":"Condition 8 - Expected Value","info":"Value to compare against (not used for exists/is_empty operators)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_8_operator","field_value":"exists","operator":"not_equals"},{"field_name":"condition_8_operator","field_value":"is_empty","operator":"not_equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_8_use_primary","display_name":"Condition 8 - Use Primary Input Data","info":"Route primary input data when this condition matches","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_8_custom_input","display_name":"Condition 8 - Custom Input","info":"Custom data to route when condition matches (only if not using primary input)","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_8_use_primary","field_value":"False","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_9_source","display_name":"Condition 9 - Source","info":"Source of data for condition evaluation","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"node_output","options":["node_output","global_context"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_9_variable","display_name":"Condition 9 - Variable Name","info":"Global context variable name (only for global_context source)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_9_source","field_value":"global_context","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_9_operator","display_name":"Condition 9 - Operator","info":"Comparison operator to apply","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"equals","options":["equals","not_equals","contains","starts_with","ends_with","greater_than","less_than","exists","is_empty"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_9_expected_value","display_name":"Condition 9 - Expected Value","info":"Value to compare against (not used for exists/is_empty operators)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_9_operator","field_value":"exists","operator":"not_equals"},{"field_name":"condition_9_operator","field_value":"is_empty","operator":"not_equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_9_use_primary","display_name":"Condition 9 - Use Primary Input Data","info":"Route primary input data when this condition matches","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_9_custom_input","display_name":"Condition 9 - Custom Input","info":"Custom data to route when condition matches (only if not using primary input)","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_9_use_primary","field_value":"False","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_10_source","display_name":"Condition 10 - Source","info":"Source of data for condition evaluation","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"node_output","options":["node_output","global_context"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_10_variable","display_name":"Condition 10 - Variable Name","info":"Global context variable name (only for global_context source)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_10_source","field_value":"global_context","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_10_operator","display_name":"Condition 10 - Operator","info":"Comparison operator to apply","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"equals","options":["equals","not_equals","contains","starts_with","ends_with","greater_than","less_than","exists","is_empty"],"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_10_expected_value","display_name":"Condition 10 - Expected Value","info":"Value to compare against (not used for exists/is_empty operators)","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_10_operator","field_value":"exists","operator":"not_equals"},{"field_name":"condition_10_operator","field_value":"is_empty","operator":"not_equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_10_use_primary","display_name":"Condition 10 - Use Primary Input Data","info":"Route primary input data when this condition matches","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_10_custom_input","display_name":"Condition 10 - Custom Input","info":"Custom data to route when condition matches (only if not using primary input)","input_type":"multiline","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_10_use_primary","field_value":"False","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_1_input_handle","display_name":"Condition 1 Input","info":"Input data for condition 1 evaluation (only when source is Node Output)","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"condition_1_source","field_value":"node_output","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_2_input_handle","display_name":"Condition 2 Input","info":"Input data for condition 2 evaluation (only when source is Node Output)","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"condition_2_source","field_value":"node_output","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_3_input_handle","display_name":"Condition 3 Input","info":"Input data for condition 3 evaluation (only when source is Node Output)","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"1","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_3_source","field_value":"node_output","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_4_input_handle","display_name":"Condition 4 Input","info":"Input data for condition 4 evaluation (only when source is Node Output)","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"2","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_4_source","field_value":"node_output","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_5_input_handle","display_name":"Condition 5 Input","info":"Input data for condition 5 evaluation (only when source is Node Output)","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"3","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_5_source","field_value":"node_output","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_6_input_handle","display_name":"Condition 6 Input","info":"Input data for condition 6 evaluation (only when source is Node Output)","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"4","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_6_source","field_value":"node_output","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_7_input_handle","display_name":"Condition 7 Input","info":"Input data for condition 7 evaluation (only when source is Node Output)","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"5","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_7_source","field_value":"node_output","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_8_input_handle","display_name":"Condition 8 Input","info":"Input data for condition 8 evaluation (only when source is Node Output)","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"6","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_8_source","field_value":"node_output","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_9_input_handle","display_name":"Condition 9 Input","info":"Input data for condition 9 evaluation (only when source is Node Output)","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"7","operator":"equals"},{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_9_source","field_value":"node_output","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"},{"name":"condition_10_input_handle","display_name":"Condition 10 Input","info":"Input data for condition 10 evaluation (only when source is Node Output)","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":[{"field_name":"num_additional_conditions","field_value":"8","operator":"equals"},{"field_name":"condition_10_source","field_value":"node_output","operator":"equals"}],"visibility_logic":"AND","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"default_output","display_name":"Default","output_type":"Any","method":null}],"is_valid":true,"path":"components.logic.conditionalnode","interface_issues":[]},"LoopNode":{"name":"LoopNode","display_name":"For Each Loop","description":"Iterates over an input list, outputting each item. Aggregates results optionally.","category":"Logic","icon":"Repeat","beta":false,"requires_approval":false,"inputs":[{"name":"input_list","display_name":"List to Iterate","info":"Connect the list of items to loop through.","input_type":"handle","input_types":["list","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"loop_input","display_name":"Item Result (for Aggregation)","info":"Connect the result from the loop's body for the *previous* item here if you want to aggregate results.","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"item","display_name":"Current Item","output_type":"Any","method":null},{"name":"index","display_name":"Current Index","output_type":"int","method":null},{"name":"done","display_name":"Done (Aggregated)","output_type":"list","method":null},{"name":"is_looping","display_name":"Is Looping?","output_type":"bool","method":null}],"is_valid":true,"path":"components.logic.loopnode","interface_issues":[]},"SwitchNodeDynamic":{"name":"SwitchNodeDynamic","display_name":"Switch (Dynamic Cases)","description":"Routes data based on matching against a user-defined list of cases. Requires Frontend support for dynamic handles.","category":"Logic","icon":"GitMerge","beta":true,"requires_approval":false,"inputs":[{"name":"input_value_compare_handle","display_name":"Input Value (to Compare)","info":"Connect the value to compare against the different cases.","input_type":"handle","input_types":["Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_value_compare","display_name":"Input Value (Direct)","info":"The value to compare against the different cases. Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"cases","display_name":"Cases (Match Values)","info":"List of string values to match against. Add/remove cases using UI controls. Frontend renders corresponding outputs.","input_type":"list","input_types":null,"required":false,"is_handle":false,"is_list":true,"real_time_refresh":false,"advanced":false,"value":[],"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"case_sensitive","display_name":"Case Sensitive Match","info":"If checked, the comparison will be case-sensitive.","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":false,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"routed_input_handle","display_name":"Input Value (to Route)","info":"Connect the actual data/message to pass through the selected output route.","input_type":"handle","input_types":["Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"routed_input","display_name":"Input Value (Direct)","info":"The data/message to pass through the selected output route. Used if no connection is provided.","input_type":"multiline","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"default_output","display_name":"Default","output_type":"Any","method":null}],"is_valid":true,"path":"components.logic.switchnodedynamic","interface_issues":[]}},"Data Interaction":{"ApiRequestNode":{"name":"ApiRequestNode","display_name":"API Request","description":"Makes a single HTTP request to the specified URL.","category":"Data Interaction","icon":"Globe","beta":false,"requires_approval":false,"inputs":[{"name":"url","display_name":"URL","info":"The URL to make the request to.","input_type":"string","input_types":null,"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":true,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"method","display_name":"HTTP Method","info":"The HTTP method to use for the request.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":true,"advanced":false,"value":"GET","options":["GET","POST","PUT","PATCH","DELETE"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"query_params","display_name":"Query Parameters","info":"Key-value pairs to append to the URL query string (optional).","input_type":"dict","input_types":null,"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":true,"value":{},"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"headers","display_name":"Request Headers","info":"Key-value pairs for request headers (optional).","input_type":"dict","input_types":null,"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":true,"value":{},"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"body","display_name":"Request Body (JSON)","info":"Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized.","input_type":"dict","input_types":null,"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":true,"value":{},"options":null,"visibility_rules":[{"field_name":"method","field_value":"POST","operator":"equals"},{"field_name":"method","field_value":"PUT","operator":"equals"},{"field_name":"method","field_value":"PATCH","operator":"equals"}],"visibility_logic":"OR","requirement_rules":[{"field_name":"method","field_value":"POST","operator":"equals"},{"field_name":"method","field_value":"PUT","operator":"equals"},{"field_name":"method","field_value":"PATCH","operator":"equals"}],"requirement_logic":"OR"},{"name":"timeout","display_name":"Timeout (seconds)","info":"Maximum time to wait for a response.","input_type":"int","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":10,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"follow_redirects","display_name":"Follow Redirects","info":"Automatically follow HTTP redirects (e.g., 301, 302).","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":true,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"save_to_file","display_name":"Save Response to File","info":"Save response body to a temporary file instead of returning content directly. 'Result' output will be the file path.","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":false,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"output_format","display_name":"Output Format","info":"'auto': Try JSON, fallback to text. 'json': Force JSON parsing (error if invalid). 'text': Return as text. 'bytes': Return raw bytes. 'file_path': Always save to file (requires 'Save Response to File'). 'metadata_dict': Return dict with status, headers, body/path, error.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":"auto","options":["auto","json","text","bytes","file_path","metadata_dict"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"raise_on_error","display_name":"Raise Exception on HTTP Error","info":"Stop workflow execution if an HTTP error status (4xx, 5xx) is received.","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":false,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"data","display_name":"Response Data/Body","output_type":"Any","method":null},{"name":"status_code","display_name":"Status Code","output_type":"int","method":null},{"name":"response_headers","display_name":"Response Headers","output_type":"dict","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.data interaction.apirequestnode","interface_issues":[]},"WebhookComponent":{"name":"WebhookComponent","display_name":"Webhook Trigger","description":"Receives data from an external system via HTTP request.","category":"Data Interaction","icon":"Globe","beta":true,"requires_approval":false,"inputs":[{"name":"method","display_name":"Allowed Method","info":"HTTP method the webhook endpoint will accept. Set by framework.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":"POST","options":["POST","GET","ANY"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"require_auth","display_name":"Require Authentication","info":"If enabled, framework requires requests to include a valid auth token.","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":false,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"auth_token","display_name":"Auth Token","info":"Secret token framework uses to validate requests if auth is required.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":"","options":null,"visibility_rules":[{"field_name":"require_auth","field_value":"True","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"webhook_url","display_name":"Webhook URL","info":"The unique URL for triggering this workflow. Send HTTP requests here.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"FRAMEWORK_GENERATED_URL","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"curl_example","display_name":"cURL Example","info":"","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":"FRAMEWORK_GENERATED_CURL","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"received_headers","display_name":"Received Headers","info":"","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"received_query_params","display_name":"Received Query Params","info":"","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"received_body","display_name":"Received Body","info":"","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"received_method","display_name":"Received Method","info":"","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"headers","display_name":"Request Headers","output_type":"dict","method":null},{"name":"query_params","display_name":"Query Parameters","output_type":"dict","method":null},{"name":"body","display_name":"Request Body","output_type":"Any","method":null},{"name":"webhook_url_out","display_name":"Webhook URL","output_type":"string","method":null},{"name":"method_out","display_name":"Request Method","output_type":"string","method":null},{"name":"trigger_data","display_name":"Trigger Data (Combined)","output_type":"dict","method":null}],"is_valid":true,"path":"components.data interaction.webhookcomponent","interface_issues":[]}},"Helpers":{"DocExtractorComponent":{"name":"DocExtractorComponent","display_name":"Document Extractor","description":"Extracts text content from document files (PDF, DOCX, TXT).","category":"Helpers","icon":"FileText","beta":false,"requires_approval":false,"inputs":[{"name":"file_path_handle","display_name":"File Path","info":"Connect a file path from another node.","input_type":"handle","input_types":["string"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"file_path","display_name":"File Path (Direct)","info":"Path to the document file. Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"file_type","display_name":"File Type","info":"The type of document file to extract from.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"Auto-Detect","options":["Auto-Detect","PDF","DOCX","TXT"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"text_content","display_name":"Text Content","output_type":"string","method":null},{"name":"page_count","display_name":"Page Count","output_type":"int","method":null},{"name":"error","display_name":"Error","output_type":"string","method":null}],"is_valid":true,"path":"components.helpers.docextractorcomponent","interface_issues":[]},"IDGeneratorComponent":{"name":"IDGeneratorComponent","display_name":"ID Generator","description":"Generates various types of unique identifiers (UUID, timestamp, short ID).","category":"Helpers","icon":"Fingerprint","beta":false,"requires_approval":false,"inputs":[{"name":"id_type","display_name":"ID Type","info":"The type of unique identifier to generate.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"UUIDv4","options":["UUIDv4","Timestamp ID","Short ID"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"short_id_length","display_name":"Short ID Length","info":"The length of the short ID (only used when ID Type is 'Short ID').","input_type":"int","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":8,"options":null,"visibility_rules":[{"field_name":"id_type","field_value":"Short ID","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"unique_id","display_name":"Unique ID","output_type":"string","method":null},{"name":"error","display_name":"Error","output_type":"string","method":null}],"is_valid":true,"path":"components.helpers.idgeneratorcomponent","interface_issues":[]}},"IO":{"StartNode":{"name":"StartNode","display_name":"Start","description":"The starting point for all workflows. Only nodes connected to this node will be executed.","category":"IO","icon":"Play","beta":false,"requires_approval":false,"inputs":[],"outputs":[{"name":"flow","display_name":"Flow","output_type":"Any","method":null}],"is_valid":true,"path":"components.io.startnode","interface_issues":[]}},"Processing":{"AlterMetadataComponent":{"name":"AlterMetadataComponent","display_name":"Alter Metadata","description":"Modifies metadata dictionary keys.","category":"Processing","icon":"Tag","beta":false,"requires_approval":false,"inputs":[{"name":"input_metadata","display_name":"Input Metadata","info":"The metadata dictionary to modify. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"updates","display_name":"Metadata Updates","info":"Dictionary of key-value pairs to update or add to the metadata. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"keys_to_remove","display_name":"Keys to Remove","info":"List of keys to remove from the metadata. Can be connected from another node or entered directly.","input_type":"list","input_types":["list","Any"],"required":false,"is_handle":true,"is_list":true,"real_time_refresh":false,"advanced":false,"value":[],"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"output_metadata","display_name":"Updated Metadata","output_type":"dict","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.processing.altermetadatacomponent","interface_issues":[]},"CombineTextComponent":{"name":"CombineTextComponent","display_name":"Combine Text","description":"Joins text inputs with a separator, supporting a variable number of inputs.","category":"Processing","icon":"Link","beta":false,"requires_approval":false,"inputs":[{"name":"main_input","display_name":"Main Input","info":"The main text or list to combine. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"num_additional_inputs","display_name":"Number of Additional Inputs","info":"Set the number of additional text inputs to show (1-10).","input_type":"int","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":2,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"separator","display_name":"Separator","info":"The character or string to join the text with.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"\\n","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_1","display_name":"Input 1","info":"Text for input 1. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"1","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"2","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"3","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"4","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"5","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_2","display_name":"Input 2","info":"Text for input 2. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"2","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"3","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"4","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"5","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_3","display_name":"Input 3","info":"Text for input 3. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"3","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"4","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"5","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_4","display_name":"Input 4","info":"Text for input 4. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"4","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"5","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_5","display_name":"Input 5","info":"Text for input 5. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"5","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_6","display_name":"Input 6","info":"Text for input 6. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_7","display_name":"Input 7","info":"Text for input 7. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_8","display_name":"Input 8","info":"Text for input 8. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_9","display_name":"Input 9","info":"Text for input 9. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_10","display_name":"Input 10","info":"Text for input 10. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"result","display_name":"Combined Text","output_type":"string","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.processing.combinetextcomponent","interface_issues":[]},"DataToDataFrameComponent":{"name":"DataToDataFrameComponent","display_name":"Data to DataFrame","description":"Converts data to a Pandas DataFrame.","category":"Processing","icon":"Table","beta":false,"requires_approval":false,"inputs":[{"name":"input_data","display_name":"Input Data","info":"The data to convert to a DataFrame. Can be connected from another node or entered directly.","input_type":"dict","input_types":["list","dict","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"orientation","display_name":"Data Orientation","info":"The orientation of the input data: 'records' (list of dicts), 'columns' (dict of lists), or 'auto-detect'.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"auto-detect","options":["records","columns","auto-detect"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"output_dataframe","display_name":"DataFrame","output_type":"DataFrame","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.processing.datatodataframecomponent","interface_issues":[]},"MergeDataComponent":{"name":"MergeDataComponent","display_name":"Merge Data","description":"Combines multiple dictionaries or lists.","category":"Processing","icon":"Combine","beta":false,"requires_approval":false,"inputs":[{"name":"main_input","display_name":"Main Input","info":"The main data structure to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"num_additional_inputs","display_name":"Number of Additional Inputs","info":"Set the number of additional inputs to show (1-10).","input_type":"int","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":2,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"merge_strategy","display_name":"Merge Strategy (Dicts)","info":"How to handle conflicts when merging dictionaries.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"Overwrite","options":["Overwrite","Deep Merge","Error on Conflict"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_1","display_name":"Input 1","info":"Data structure 1 to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"1","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"2","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"3","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"4","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"5","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_2","display_name":"Input 2","info":"Data structure 2 to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"2","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"3","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"4","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"5","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_3","display_name":"Input 3","info":"Data structure 3 to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"3","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"4","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"5","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_4","display_name":"Input 4","info":"Data structure 4 to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"4","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"5","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_5","display_name":"Input 5","info":"Data structure 5 to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"5","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_6","display_name":"Input 6","info":"Data structure 6 to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"6","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_7","display_name":"Input 7","info":"Data structure 7 to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"7","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_8","display_name":"Input 8","info":"Data structure 8 to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"8","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_9","display_name":"Input 9","info":"Data structure 9 to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"9","operator":"equals"},{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"input_10","display_name":"Input 10","info":"Data structure 10 to merge. Can be connected from another node or entered directly.","input_type":"dict","input_types":["dict","list","Any"],"required":false,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":[{"field_name":"num_additional_inputs","field_value":"10","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"output_data","display_name":"Merged Data","output_type":"Any","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.processing.mergedatacomponent","interface_issues":[]},"MessageToDataComponent":{"name":"MessageToDataComponent","display_name":"Message To Data","description":"Extracts fields from a Message object.","category":"Processing","icon":"Package","beta":false,"requires_approval":false,"inputs":[{"name":"input_message","display_name":"Input Message","info":"The Message object to extract fields from. Can be connected from another node or entered directly.","input_type":"dict","input_types":["Message","dict","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"fields_to_extract","display_name":"Fields to Extract","info":"List of field names to extract from the message. Leave empty to extract all fields. Can be connected from another node or entered directly.","input_type":"list","input_types":["list","Any"],"required":false,"is_handle":true,"is_list":true,"real_time_refresh":false,"advanced":false,"value":[],"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"output_data","display_name":"Extracted Data","output_type":"dict","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.processing.messagetodatacomponent","interface_issues":[]},"SaveToFileComponent":{"name":"SaveToFileComponent","display_name":"Save To File","description":"Writes data to a file (text or JSON).","category":"Processing","icon":"Save","beta":false,"requires_approval":false,"inputs":[{"name":"input_data_handle","display_name":"Data to Save","info":"Connect data from another node to save to a file.","input_type":"handle","input_types":["string","dict","list","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"file_path_handle","display_name":"File Path","info":"Connect a string containing the file path.","input_type":"handle","input_types":["string"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":null,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"file_path","display_name":"File Path (Direct)","info":"The path where the file will be saved. Used if no connection is provided.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"overwrite","display_name":"Overwrite","info":"If enabled, overwrites existing files. If disabled, returns an error if the file exists.","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"encoding","display_name":"Encoding","info":"The character encoding to use when writing the file.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"utf-8","options":["utf-8","latin-1","ascii"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"create_dirs","display_name":"Create Directories","info":"If enabled, creates any missing directories in the file path.","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":true,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"file_path_out","display_name":"File Path Saved","output_type":"string","method":null},{"name":"success","display_name":"Success","output_type":"bool","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.processing.savetofilecomponent","interface_issues":[]},"SelectDataComponent":{"name":"SelectDataComponent","display_name":"Select Data","description":"Extracts elements from lists or dictionaries.","category":"Processing","icon":"Filter","beta":false,"requires_approval":false,"inputs":[{"name":"input_data","display_name":"Input Data","info":"The data to select from. Can be connected from another node or entered directly.","input_type":"dict","input_types":["list","dict","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":{},"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"data_type","display_name":"Data Type","info":"The type of data structure to select from.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"Auto-Detect","options":["Auto-Detect","List","Dictionary"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"search_mode","display_name":"Search Mode","info":"Exact Path: Use precise path notation (e.g., 'user.name'). Smart Search: Find field by name anywhere in the structure (e.g., 'email' finds first occurrence).","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"Exact Path","options":["Exact Path","Smart Search"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"field_matching_mode","display_name":"Field Matching Mode","info":"Auto-detect: Try key-based first, fallback to property-based. Key-based Only: Traditional object keys. Property-based Only: Search property_name fields. Use @ notation for property-based paths (e.g., 'result.@script').","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"Auto-detect","options":["Auto-detect","Key-based Only","Property-based Only"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"selector","display_name":"Selector","info":"For lists: index or slice (e.g., '0', '1:5'). For dicts: key name or path (e.g., 'user.name') in Exact Path mode, or field name (e.g., 'email') in Smart Search mode. Use @ notation for property-based paths (e.g., 'result.@script' finds object where property_name='script'). Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"output_data","display_name":"Selected Data","output_type":"Any","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.processing.selectdatacomponent","interface_issues":[]},"SplitTextComponent":{"name":"SplitTextComponent","display_name":"Split Text","description":"Splits text into a list using a delimiter.","category":"Processing","icon":"Scissors","beta":false,"requires_approval":false,"inputs":[{"name":"input_text","display_name":"Input Text","info":"The text to split. Can be connected from another node or entered directly.","input_type":"string","input_types":["string","Any"],"required":true,"is_handle":true,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"delimiter","display_name":"Delimiter","info":"The character or string to split the text by.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":",","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"max_splits","display_name":"Max Splits","info":"Maximum number of splits to perform. -1 means no limit.","input_type":"int","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":-1,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"include_delimiter","display_name":"Include Delimiter","info":"If enabled, the delimiter will be included at the end of each split part (except the last one).","input_type":"bool","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":false,"options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"output_list","display_name":"Split List","output_type":"list","method":null},{"name":"error","display_name":"Error","output_type":"str","method":null}],"is_valid":true,"path":"components.processing.splittextcomponent","interface_issues":[]}},"Tools":{"MCPToolsComponent":{"name":"MCPToolsComponent","display_name":"MCP Tools","description":"Interact with MCP tools via Stdio or SSE.","category":"Tools","icon":"Tool","beta":false,"requires_approval":false,"inputs":[{"name":"mode","display_name":"Connection Mode","info":"The mode to use for connecting to MCP tools.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"Stdio","options":["Stdio","SSE"],"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"command","display_name":"Stdio Command","info":"The command to run for Stdio mode. Enter command and click 'Fetch Tools'.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"mode","field_value":"Stdio","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"fetch_stdio_tools","display_name":"Fetch Tools","info":"Click to connect and fetch tools using the Stdio command.","input_type":"button","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"False","options":null,"visibility_rules":[{"field_name":"mode","field_value":"Stdio","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"sse_url","display_name":"SSE URL","info":"The URL of the SSE server. Enter URL and click 'Fetch Tools'.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"mode","field_value":"SSE","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"fetch_sse_tools","display_name":"Fetch Tools","info":"Click to connect and fetch tools using the SSE URL.","input_type":"button","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"False","options":null,"visibility_rules":[{"field_name":"mode","field_value":"SSE","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"connection_status","display_name":"Connection Status","info":"Current connection status.","input_type":"string","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":true,"value":"Not Connected","options":null,"visibility_rules":null,"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"},{"name":"selected_tool_name","display_name":"Tool","info":"Select the MCP tool to use.","input_type":"dropdown","input_types":null,"required":false,"is_handle":false,"is_list":false,"real_time_refresh":false,"advanced":false,"value":"","options":null,"visibility_rules":[{"field_name":"connection_status","field_value":"Connected","operator":"equals"}],"visibility_logic":"OR","requirement_rules":null,"requirement_logic":"OR"}],"outputs":[{"name":"result","display_name":"Result","output_type":"dict","method":null},{"name":"combined_text","display_name":"Combined Text","output_type":"string","method":null},{"name":"error","display_name":"Error","output_type":"string","method":null}],"is_valid":true,"path":"components.tools.mcptoolscomponent","interface_issues":[]}}}
