import time
from typing import List, Dict, Any, ClassVar

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    DropdownInput,
    StringInput,
    BoolInput,
    IntInput,
    HandleInput,
    InputVisibilityRule,
    Output,
)
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import <PERSON>deR<PERSON>ult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input


class ConditionalNode(BaseNode):
    """
    Switch-Case Router: Pure routing component that generates conditional logic for workflow transitions.

    **IMPORTANT ARCHITECTURAL NOTE:**
    This component does NOT execute as a separate workflow node. Instead, it generates
    conditional routing logic that is embedded into the previous node's transition in the
    transition schema. The orchestration engine handles all conditional evaluation and routing.

    This component supports up to 10 conditions with 9 different operators:
    equals, not_equals, contains, starts_with, ends_with, greater_than, less_than, exists, is_empty

    Each condition can use either node_output or global_context as source,
    and can route either primary input data or custom per-condition input data.

    **Frontend Requirements:**
    - Monitor the 'num_conditions' input value changes (1-10)
    - Dynamically render output handles: condition_1_output, condition_2_output, ..., condition_N_output, default_output
    - Update handle connections when num_conditions changes
    - Store connections from these dynamic handles with sourceHandle="condition_{i}_output"
    - Remove/hide output handles when num_conditions decreases

    **Workflow Schema Generation:**
    - This node does NOT appear in the transition schema's 'nodes' array
    - Instead, it generates 'conditional_routing' logic embedded in the previous node's transition
    - The orchestration engine processes conditional routing directly without separate node execution
    """

    name = "ConditionalNode"
    display_name = "Switch-Case Router"
    description = "Evaluates multiple conditions and routes data to matching outputs"
    category = "Logic"
    icon = "GitBranch"
    beta = False

    # Supported operators for switch-case conditions
    OPERATOR_OPTIONS = [
        "equals", "not_equals", "contains", "starts_with", "ends_with",
        "greater_than", "less_than", "exists", "is_empty"
    ]

    # Source options for condition evaluation
    SOURCE_OPTIONS = ["node_output", "global_context"]

    inputs: ClassVar[List[InputBase]] = [
        # Primary input data (optional) - shared across conditions
        create_dual_purpose_input(
            name="primary_input_data",
            display_name="Primary Input Data",
            input_type="multiline",
            required=False,
            info="Main data to route through conditions. Can be connected from another node or entered directly.",
            input_types=["Any"],
        ),

        # Condition 1 configuration
        DropdownInput(
            name="condition_1_source",
            display_name="Condition 1 - Source",
            options=SOURCE_OPTIONS,
            value="node_output",
            required=False,
            info="Source of data for condition evaluation",
        ),
        StringInput(
            name="condition_1_variable",
            display_name="Condition 1 - Variable Name",
            value="",
            required=False,
            info="Global context variable name (only for global_context source)",
            visibility_rules=[InputVisibilityRule(
                field_name="condition_1_source",
                field_value="global_context"
            )],
        ),
        DropdownInput(
            name="condition_1_operator",
            display_name="Condition 1 - Operator",
            options=OPERATOR_OPTIONS,
            value="equals",
            required=True,
            info="Comparison operator to apply",
        ),
        StringInput(
            name="condition_1_expected_value",
            display_name="Condition 1 - Expected Value",
            value="",
            required=False,
            info="Value to compare against (not used for exists/is_empty operators)",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="condition_1_operator",
                    field_value="exists",
                    operator="not_equals"
                ),
                InputVisibilityRule(
                    field_name="condition_1_operator",
                    field_value="is_empty",
                    operator="not_equals"
                )
            ],
            visibility_logic="AND",
        ),
        BoolInput(
            name="condition_1_use_primary",
            display_name="Condition 1 - Use Primary Input Data",
            value=True,
            info="Route primary input data when this condition matches",
        ),
        create_dual_purpose_input(
            name="condition_1_custom_input",
            display_name="Condition 1 - Custom Input",
            input_type="multiline",
            required=False,
            info="Custom data to route when condition matches (only if not using primary input)",
            input_types=["Any"],
            visibility_rules=[InputVisibilityRule(
                field_name="condition_1_use_primary",
                field_value=False
            )],
        ),

        # Condition 2 configuration (similar structure)
        DropdownInput(
            name="condition_2_source",
            display_name="Condition 2 - Source",
            options=SOURCE_OPTIONS,
            value="node_output",
            required=True,
            info="Source of data for condition evaluation",
        ),
        StringInput(
            name="condition_2_variable",
            display_name="Condition 2 - Variable Name",
            value="",
            required=False,
            info="Global context variable name (only for global_context source)",
            visibility_rules=[InputVisibilityRule(
                field_name="condition_2_source",
                field_value="global_context"
            )],
        ),
        DropdownInput(
            name="condition_2_operator",
            display_name="Condition 2 - Operator",
            options=OPERATOR_OPTIONS,
            value="equals",
            required=True,
            info="Comparison operator to apply",
        ),
        StringInput(
            name="condition_2_expected_value",
            display_name="Condition 2 - Expected Value",
            value="",
            required=False,
            info="Value to compare against (not used for exists/is_empty operators)",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="condition_2_operator",
                    field_value="exists",
                    operator="not_equals"
                ),
                InputVisibilityRule(
                    field_name="condition_2_operator",
                    field_value="is_empty",
                    operator="not_equals"
                )
            ],
            visibility_logic="AND",
        ),
        BoolInput(
            name="condition_2_use_primary",
            display_name="Condition 2 - Use Primary Input Data",
            value=True,
            info="Route primary input data when this condition matches",
        ),
        create_dual_purpose_input(
            name="condition_2_custom_input",
            display_name="Condition 2 - Custom Input",
            input_type="multiline",
            required=False,
            info="Custom data to route when condition matches (only if not using primary input)",
            input_types=["Any"],
            visibility_rules=[InputVisibilityRule(
                field_name="condition_2_use_primary",
                field_value=False
            )],
        ),

        # Dynamic condition management
        IntInput(
            name="num_additional_conditions",
            display_name="Number of Additional Conditions",
            value=0,
            min_value=0,
            max_value=8,
            info="Number of additional conditions beyond the base 2 conditions (0-8). Total conditions will be 2 + this value.",
        ),
    ]

    # Add dynamic condition inputs for conditions 3-10
    MAX_ADDITIONAL_CONDITIONS = 8
    BASE_CONDITIONS = 2

    for i in range(3, BASE_CONDITIONS + MAX_ADDITIONAL_CONDITIONS + 1):  # 3 to 10
        # Create visibility rules - show if num_additional_conditions >= (i - BASE_CONDITIONS)
        # For condition 3: show if num_additional_conditions >= 1
        # For condition 4: show if num_additional_conditions >= 2, etc.
        min_additional_needed = i - BASE_CONDITIONS
        visibility_rules = [
            InputVisibilityRule(field_name="num_additional_conditions", field_value=j)
            for j in range(min_additional_needed, MAX_ADDITIONAL_CONDITIONS + 1)
        ]

        # Condition source
        inputs.append(DropdownInput(
            name=f"condition_{i}_source",
            display_name=f"Condition {i} - Source",
            options=SOURCE_OPTIONS,
            value="node_output",
            required=False,  # Changed to False to prevent start node collection
            info="Source of data for condition evaluation",
            visibility_rules=visibility_rules,
        ))

        # Variable name (only for global_context)
        variable_visibility_rules = visibility_rules + [InputVisibilityRule(
            field_name=f"condition_{i}_source",
            field_value="global_context"
        )]
        inputs.append(StringInput(
            name=f"condition_{i}_variable",
            display_name=f"Condition {i} - Variable Name",
            value="",
            required=False,
            info="Global context variable name (only for global_context source)",
            visibility_rules=variable_visibility_rules,
            visibility_logic="AND",
        ))

        # Operator
        inputs.append(DropdownInput(
            name=f"condition_{i}_operator",
            display_name=f"Condition {i} - Operator",
            options=OPERATOR_OPTIONS,
            value="equals",
            required=False,  # Changed to False to prevent start node collection
            info="Comparison operator to apply",
            visibility_rules=visibility_rules,
        ))

        # Expected value (hidden for exists/is_empty)
        expected_value_visibility_rules = visibility_rules + [
            InputVisibilityRule(
                field_name=f"condition_{i}_operator",
                field_value="exists",
                operator="not_equals"
            ),
            InputVisibilityRule(
                field_name=f"condition_{i}_operator",
                field_value="is_empty",
                operator="not_equals"
            )
        ]
        inputs.append(StringInput(
            name=f"condition_{i}_expected_value",
            display_name=f"Condition {i} - Expected Value",
            value="",
            required=False,
            info="Value to compare against (not used for exists/is_empty operators)",
            visibility_rules=expected_value_visibility_rules,
            visibility_logic="AND",
        ))

        # Use primary input toggle
        inputs.append(BoolInput(
            name=f"condition_{i}_use_primary",
            display_name=f"Condition {i} - Use Primary Input Data",
            value=True,
            info="Route primary input data when this condition matches",
            visibility_rules=visibility_rules,
        ))

        # Custom input (only when not using primary)
        custom_input_visibility_rules = visibility_rules + [InputVisibilityRule(
            field_name=f"condition_{i}_use_primary",
            field_value=False
        )]
        inputs.append(create_dual_purpose_input(
            name=f"condition_{i}_custom_input",
            display_name=f"Condition {i} - Custom Input",
            input_type="multiline",
            required=False,
            info="Custom data to route when condition matches (only if not using primary input)",
            input_types=["Any"],
            visibility_rules=custom_input_visibility_rules,
            visibility_logic="AND",
        ))

    # Add dynamic input handles for each condition (when source is "node_output")
    for i in range(1, BASE_CONDITIONS + MAX_ADDITIONAL_CONDITIONS + 1):  # 1 to 10
        # For base conditions (1-2), always show when source is node_output
        if i <= BASE_CONDITIONS:
            visibility_rules = [InputVisibilityRule(
                field_name=f"condition_{i}_source",
                field_value="node_output"
            )]
        else:
            # For additional conditions (3-10), show when both:
            # 1. num_additional_conditions >= (i - BASE_CONDITIONS)
            # 2. condition_i_source == "node_output"
            min_additional_needed = i - BASE_CONDITIONS
            condition_visibility_rules = [
                InputVisibilityRule(field_name="num_additional_conditions", field_value=j)
                for j in range(min_additional_needed, MAX_ADDITIONAL_CONDITIONS + 1)
            ]
            visibility_rules = condition_visibility_rules + [InputVisibilityRule(
                field_name=f"condition_{i}_source",
                field_value="node_output"
            )]

        inputs.append(HandleInput(
            name=f"condition_{i}_input_handle",
            display_name=f"Condition {i} Input",
            input_types=["Any"],
            required=False,
            info=f"Input data for condition {i} evaluation (only when source is Node Output)",
            visibility_rules=visibility_rules,
            visibility_logic="AND",
        ))

    # Define maximum conditions for dynamic output generation
    MAX_CONDITIONS = 10

    # Static outputs - only default is defined here
    # Dynamic outputs (condition_1_output, condition_2_output, ..., condition_N_output)
    # are handled by the frontend based on num_additional_conditions value
    outputs: ClassVar[List[Output]] = [
        Output(
            name="default_output",
            display_name="Default",
            output_type="Any",
            info="Outputs data when no conditions match",
        ),
        # NOTE: condition_1_output, condition_2_output, ..., condition_N_output are NOT defined here statically.
        # They are expected to be handled dynamically by the frontend based on 'num_additional_conditions' input.
        # Frontend should render: condition_1_output, condition_2_output, ..., condition_N_output
        # where N = 2 + value of 'num_additional_conditions' input (2-10 total conditions)
    ]

    def get_dynamic_output_keys(self, num_additional_conditions: int) -> List[str]:
        """
        Get all dynamic output keys for the given number of additional conditions.

        Args:
            num_additional_conditions: Number of additional conditions beyond base 2 (0-8)

        Returns:
            List of output keys including condition outputs and default
        """
        num_additional_conditions = max(0, min(self.MAX_ADDITIONAL_CONDITIONS, int(num_additional_conditions)))
        total_conditions = self.BASE_CONDITIONS + num_additional_conditions
        return [f"condition_{i}_output" for i in range(1, total_conditions + 1)] + ["default_output"]

    def get_dynamic_outputs(self, config: Dict[str, Any]) -> List[Output]:
        """
        Get dynamic output definitions based on configuration.
        This method is called by the frontend to generate dynamic output handles.

        Args:
            config: Component configuration containing num_additional_conditions

        Returns:
            List of dynamic Output objects for the frontend
        """
        num_additional_conditions = config.get("num_additional_conditions", 0)
        num_additional_conditions = max(0, min(self.MAX_ADDITIONAL_CONDITIONS, int(num_additional_conditions)))
        total_conditions = self.BASE_CONDITIONS + num_additional_conditions

        dynamic_outputs = []

        # Add condition outputs
        for i in range(1, total_conditions + 1):
            dynamic_outputs.append(Output(
                name=f"condition_{i}_output",
                display_name=f"Condition {i}",
                output_type="Any",
                info=f"Outputs data when condition {i} matches",
            ))

        # Add default output
        dynamic_outputs.append(Output(
            name="default_output",
            display_name="Default",
            output_type="Any",
            info="Outputs data when no conditions match",
        ))

        return dynamic_outputs

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get input value from context, prioritizing handle inputs over direct inputs.

        Args:
            input_name: Name of the input to retrieve
            context: The workflow execution context
            default: Default value if input is not found

        Returns:
            The input value or default if not found
        """
        node_id = context.current_node_id
        if not node_id:
            return default

        # Check if there's a value in the node outputs
        node_outputs = context.node_outputs.get(node_id, {})
        if input_name in node_outputs:
            return node_outputs[input_name]

        # If not found, return the default
        return default

    def _evaluate_condition(self, condition_config: Dict[str, Any], input_data: Any, global_context: Dict[str, Any]) -> bool:
        """
        Evaluate a single condition based on configuration.

        Args:
            condition_config: Dictionary containing condition configuration
            input_data: Input data for node_output source
            global_context: Global context variables

        Returns:
            True if condition matches, False otherwise
        """
        try:
            operator = condition_config.get("operator", "equals")
            source = condition_config.get("source", "node_output")
            expected_value = condition_config.get("expected_value", "")

            # Get the actual value to compare
            if source == "global_context":
                variable_name = condition_config.get("variable", "")
                actual_value = self._resolve_global_context_variable(variable_name, global_context)
            else:  # node_output
                actual_value = input_data

            # Evaluate based on operator
            return self._apply_operator(operator, actual_value, expected_value)

        except Exception as e:
            # Log error but don't fail the entire execution - skip this condition
            print(f"Warning: Error evaluating condition: {e}")
            return False

    def _apply_operator(self, operator: str, actual_value: Any, expected_value: Any) -> bool:
        """
        Apply the specified operator to compare actual and expected values.

        Args:
            operator: The comparison operator
            actual_value: The actual value to compare
            expected_value: The expected value to compare against

        Returns:
            True if the comparison matches, False otherwise
        """
        try:
            if operator == "equals":
                return str(actual_value) == str(expected_value)

            elif operator == "not_equals":
                return str(actual_value) != str(expected_value)

            elif operator == "contains":
                actual_str = str(actual_value) if actual_value is not None else ""
                expected_str = str(expected_value) if expected_value is not None else ""
                return expected_str in actual_str

            elif operator == "starts_with":
                actual_str = str(actual_value) if actual_value is not None else ""
                expected_str = str(expected_value) if expected_value is not None else ""
                return actual_str.startswith(expected_str)

            elif operator == "ends_with":
                actual_str = str(actual_value) if actual_value is not None else ""
                expected_str = str(expected_value) if expected_value is not None else ""
                return actual_str.endswith(expected_str)

            elif operator == "greater_than":
                try:
                    actual_num = float(actual_value)
                    expected_num = float(expected_value)
                    return actual_num > expected_num
                except (ValueError, TypeError):
                    return False

            elif operator == "less_than":
                try:
                    actual_num = float(actual_value)
                    expected_num = float(expected_value)
                    return actual_num < expected_num
                except (ValueError, TypeError):
                    return False

            elif operator == "exists":
                return actual_value is not None

            elif operator == "is_empty":
                if actual_value is None:
                    return True
                if isinstance(actual_value, str) and actual_value == "":
                    return True
                return False

            else:
                # Invalid operator - skip condition
                print(f"Warning: Unknown operator '{operator}' - skipping condition")
                return False

        except Exception as e:
            print(f"Warning: Error applying operator '{operator}': {e}")
            return False

    def _resolve_global_context_variable(self, variable_name: str, global_context: Dict[str, Any]) -> Any:
        """
        Resolve a global context variable by name.

        Args:
            variable_name: Name of the variable to resolve
            global_context: Global context dictionary

        Returns:
            The variable value or None if not found
        """
        if not variable_name:
            return None
        return global_context.get(variable_name)

    def _get_condition_input_data(self, condition_num: int, context: WorkflowContext) -> Any:
        """
        Get input data for a specific condition based on routing configuration.

        Args:
            condition_num: The condition number (1, 2, etc.)
            context: The workflow execution context

        Returns:
            The input data for the condition
        """
        source = self.get_input_value(f"condition_{condition_num}_source", context, "node_output")

        if source == "node_output":
            # For node_output source, get data from the condition's input handle
            return self.get_input_value(f"condition_{condition_num}_input_handle", context, None)
        else:
            # For global_context source, we don't need input data here
            # The actual value will be resolved in _evaluate_condition
            return None

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Modern execute method for switch-case conditional logic.

        Args:
            context: The workflow execution context

        Returns:
            NodeResult with success/error status and outputs
        """
        start_time = time.time()
        context.log(f"Executing {self.display_name}...")

        try:
            # Get number of additional conditions to evaluate
            num_additional_conditions = self.get_input_value("num_additional_conditions", context, 0)
            num_additional_conditions = max(0, min(self.MAX_ADDITIONAL_CONDITIONS, int(num_additional_conditions)))  # Clamp to 0-8
            total_conditions = self.BASE_CONDITIONS + num_additional_conditions

            # Initialize all possible dynamic outputs (following SwitchNodeDynamic pattern)
            output_keys = self.get_dynamic_output_keys(num_additional_conditions)
            outputs = {key: None for key in output_keys}  # Initialize all to None
            matched_conditions = []

            context.log(f"Evaluating {total_conditions} conditions (2 base + {num_additional_conditions} additional) with dynamic outputs: {output_keys[:-1]} + default")

            # Evaluate each condition
            for condition_num in range(1, total_conditions + 1):
                try:
                    # Get condition configuration
                    condition_config = {
                        "source": self.get_input_value(f"condition_{condition_num}_source", context, "node_output"),
                        "variable": self.get_input_value(f"condition_{condition_num}_variable", context, ""),
                        "operator": self.get_input_value(f"condition_{condition_num}_operator", context, "equals"),
                        "expected_value": self.get_input_value(f"condition_{condition_num}_expected_value", context, ""),
                    }

                    # Get input data for this condition
                    input_data = self._get_condition_input_data(condition_num, context)

                    # Evaluate the condition
                    condition_matches = self._evaluate_condition(
                        condition_config,
                        input_data,
                        context.global_context
                    )

                    if condition_matches:
                        matched_conditions.append(condition_num)
                        output_name = f"condition_{condition_num}_output"

                        # Determine what data to route for this condition
                        use_primary = self.get_input_value(f"condition_{condition_num}_use_primary", context, True)
                        if use_primary:
                            # Route primary input data
                            route_data = self.get_input_value("primary_input_data", context, None)
                        else:
                            # Route custom input data for this condition
                            route_data = self.get_input_value(f"condition_{condition_num}_custom_input", context, None)

                        # Only set output if it exists in our dynamic output keys
                        if output_name in outputs:
                            outputs[output_name] = route_data
                            context.log(f"Condition {condition_num} matched - routing to {output_name} with {'primary' if use_primary else 'custom'} data")
                        else:
                            context.log(f"Warning: Condition {condition_num} matched but output {output_name} not in dynamic outputs")

                except Exception as e:
                    context.log(f"Warning: Error evaluating condition {condition_num}: {e}")
                    # Continue with other conditions

            # If no conditions matched, route to default output
            if not matched_conditions:
                # Use primary input data for default output
                default_data = self.get_input_value("primary_input_data", context, None)
                if "default_output" in outputs:
                    outputs["default_output"] = default_data
                    context.log("No conditions matched - routing to default_output")
                else:
                    context.log("Warning: No conditions matched but default_output not in dynamic outputs")

            execution_time = time.time() - start_time
            context.log(f"Switch-case evaluation completed. Matched conditions: {matched_conditions}. Time: {execution_time:.2f}s")

            return NodeResult.success(
                outputs=outputs,
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Error in switch-case evaluation: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(error_msg, execution_time)
