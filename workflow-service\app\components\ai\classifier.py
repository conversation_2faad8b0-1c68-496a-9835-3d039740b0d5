from typing import Dict, Any, List, ClassVar
import importlib

from app.components.ai.base_agent_component import (
    BaseAgentComponent,
)
from app.models.workflow_builder.components import InputBase, HandleInput, StringInput, ListInput
from app.models.workflow_builder.components import Output
from app.constants.semantic_types import AI_ANALYSIS, AI_METADATA, ERROR_INFO


class Classifier(BaseAgentComponent):
    """
    Categorizes input text into one of several predefined classes or labels.

    This component takes input text and assigns it to one of the specified categories.
    """

    name: ClassVar[str] = "Classifier"
    display_name: ClassVar[str] = "Text Classifier"
    description: ClassVar[str] = (
        "Categorizes text into one of several predefined classes or labels."
    )

    icon: ClassVar[str] = "Tag"

    # Define component-specific inputs
    component_inputs: ClassVar[List[InputBase]] = [
        # Text to classify - connection handle
        HandleInput(
            name="text_handle",
            display_name="Text to Classify",
            required=True,
            is_handle=True,
            input_types=["string", "Any"],
            info="Connect the text that needs to be assigned a category.",
        ),
        # Text to classify - direct input in inspector
        StringInput(
            name="text",
            display_name="Text to Classify (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="The text that needs to be assigned a category. Used if no connection is provided.",
        ),
        # Categories - connection handle
        HandleInput(
            name="categories_handle",
            display_name="Categories",
            is_handle=True,
            input_types=["list", "string"],
            info="Connect a list of possible categories from another node.",
        ),
        # Categories - direct input in inspector
        ListInput(
            name="categories",
            display_name="Categories (Direct)",
            required=False,
            is_handle=False,
            value=[],
            info="The set of valid categories the text can be assigned to. Provide as a list or comma-separated string. Used if no connection is provided.",
        ),
    ]

    # Combine with model client configuration inputs from BaseAgentComponent
    inputs: ClassVar[List[InputBase]] = [
        input_def
        for input_def in BaseAgentComponent.inputs
        if input_def.name in ["model_provider", "base_url", "api_key", "model_name"]
    ] + component_inputs

    outputs: ClassVar[List[Output]] = [
        Output(name="category", display_name="Assigned Category", output_type="string", semantic_type=AI_ANALYSIS),
        Output(name="confidence", display_name="Confidence", output_type="float", semantic_type=AI_METADATA),
        Output(name="error", display_name="Error", output_type="str", semantic_type=ERROR_INFO),
    ]

    def _check_openai_installed(self) -> bool:
        """
        Checks if the openai package is installed.

        Returns:
            True if openai is installed, False otherwise.
        """
        try:
            importlib.import_module("openai")
            return True
        except ImportError:
            return False

    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the Classifier.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Classifies the provided text into one of the specified categories.

        Args:
            **kwargs: Contains the input values:
                - text: The text to classify
                - categories: The set of valid categories
                - api_key: API key for the LLM service
                - model_name: Model to use
                - temperature: Temperature for generation

        Returns:
            A dictionary with:
                - category: The assigned category
                - confidence: A confidence score between 0 and 1
                - error: An error message if the operation failed
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        print(f"Executing {self.name}...")

        # Check if openai is installed
        if not self._check_openai_installed():
            return {
                "error": "The openai package is required but not installed. Please install it with 'pip install openai'."
            }

        # Get inputs - prioritize handle inputs over direct inputs
        model_provider = kwargs.get("model_provider", "OpenAI")
        base_url = kwargs.get("base_url", "")
        api_key = kwargs.get("api_key")
        model_name = kwargs.get("model_name", "gpt-3.5-turbo")
        temperature = kwargs.get(
            "temperature", 0.3
        )  # Lower temperature for more deterministic classification

        text_handle = kwargs.get("text_handle")
        text_direct = kwargs.get("text")
        categories_handle = kwargs.get("categories_handle")
        categories_direct = kwargs.get("categories", [])

        # Process inputs - prioritize handle inputs over direct inputs
        text = text_handle if text_handle is not None else text_direct
        categories = categories_handle if categories_handle is not None else categories_direct

        # Process categories if it's a string (comma-separated)
        if isinstance(categories, str):
            categories = [cat.strip() for cat in categories.split(",") if cat.strip()]

        # Validate inputs
        if not text:
            return {
                "error": "Text to classify is missing. Please connect text or provide it directly."
            }
        if not categories or len(categories) == 0:
            return {"error": "Categories are missing. Please provide at least one category."}
        if not api_key:
            return {"error": "API key is required."}

        try:
            # Import openai
            import openai

            # Set API key and base URL if provided
            openai.api_key = api_key

            # Set base URL if provided and using custom provider
            if model_provider == "Custom" and base_url:
                openai.api_base = base_url
            elif model_provider == "Azure OpenAI":
                # For Azure, we need to set the API type and version
                openai.api_type = "azure"
                openai.api_version = "2023-05-15"
                if base_url:
                    openai.api_base = base_url

            # Create system prompt for classification
            categories_str = ", ".join([f'"{cat}"' for cat in categories])
            system_prompt = f"""You are a text classification assistant. Classify the provided text into exactly one of the following categories:
{categories_str}

Also provide a confidence score between 0 and 1, where 1 is the highest confidence.
Format your response as a JSON object with two fields: "category" and "confidence".
Example: {{"category": "Technology", "confidence": 0.85}}"""

            # Create user prompt
            user_prompt = f"Text to classify: {text}"

            # Make API call
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
            )

            # Extract content from response
            result_text = response.choices[0].message.content.strip()

            # Parse the JSON response
            import json

            try:
                result = json.loads(result_text)
                category = result.get("category", "Unknown")
                confidence = float(result.get("confidence", 0.0))

                print(
                    f"  Classification completed successfully: {category} (confidence: {confidence})"
                )
                return {"category": category, "confidence": confidence}
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract category directly from text
                for cat in categories:
                    if cat.lower() in result_text.lower():
                        print(f"  Classification completed with fallback parsing: {cat}")
                        return {
                            "category": cat,
                            "confidence": 0.5,  # Default confidence when parsing fails
                        }

                # If no category found, return the first one with low confidence
                print(f"  Classification failed to parse result, using default category")
                return {
                    "category": categories[0] if categories else "Unknown",
                    "confidence": 0.1,  # Very low confidence
                }

        except Exception as e:
            error_msg = f"Error classifying text: {str(e)}"
            print(f"  {error_msg}")
            return {"error": error_msg}
