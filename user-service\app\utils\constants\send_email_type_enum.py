from enum import Enum

class SendEmailTypeEnum(Enum):
    WELCOME = "WELCOME"
    EMAIL_VERIFICATION = "EMAIL_VERIFICATION"
    FORGOT_PASSWORD = "FORGOT_PASSWORD"

    # New organization email types
    ORG_CREATED = "ORG_CREATED"
    ORG_UPDATED = "ORG_UPDATED" 
    ORG_DELETED = "ORG_DELETED"
    ORG_USER_ADDED = "ORG_USER_ADDED"
    ORG_USER_REMOVED = "ORG_USER_REMOVED"

    # New Waitlist email types
    ADD_TO_WAITLIST = "ADD_TO_WAITLIST"
    USER_APPROVED = "USER_APPROVED"