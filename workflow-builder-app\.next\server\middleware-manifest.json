{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "a58ad3614530f6bb267c4afde2706b9e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c5460c1731ce63a105108a683380a1ff975c0f470115aef516266ec7c35b44cd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f026c49f0d7f94eb5f5c7bfd986e376bdd53adc90463e527202f13da81f16c8b"}}}, "sortedMiddleware": ["/"], "functions": {}}