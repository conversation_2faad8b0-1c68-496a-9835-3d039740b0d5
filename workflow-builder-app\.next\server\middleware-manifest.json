{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "0497f6e074b71f02bd631d2235ab53f2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d0ca5759725c5f864375840901c791778dc37d082848476c8ceab4b1e7817873", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fa7746e2eb981e240009ff213dbceb56431c6e2d4bf9b5fbe872799ff021526f"}}}, "sortedMiddleware": ["/"], "functions": {}}