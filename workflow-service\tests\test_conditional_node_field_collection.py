"""
Test to verify that conditional node dropdown values are not incorrectly collected by the start node.
This test validates the fix for the issue where conditional node dropdown configurations
were appearing in the start node's inspector panel instead of staying with the conditional node.
"""

import pytest
from app.components.control_flow.conditionalNode import ConditionalNode


class TestConditionalNodeFieldCollection:
    """Test conditional node field collection behavior."""

    def test_base_conditions_are_required(self):
        """Test that base conditions (1-2) are marked as required."""
        conditional_node = ConditionalNode()
        inputs = conditional_node.inputs

        # Find base condition inputs
        condition_1_source = next((inp for inp in inputs if inp.name == "condition_1_source"), None)
        condition_1_operator = next((inp for inp in inputs if inp.name == "condition_1_operator"), None)
        condition_2_source = next((inp for inp in inputs if inp.name == "condition_2_source"), None)
        condition_2_operator = next((inp for inp in inputs if inp.name == "condition_2_operator"), None)

        # Base conditions should be required
        assert condition_1_source is not None
        assert condition_1_source.required is True
        assert condition_1_operator is not None
        assert condition_1_operator.required is True
        assert condition_2_source is not None
        assert condition_2_source.required is True
        assert condition_2_operator is not None
        assert condition_2_operator.required is True

    def test_additional_conditions_are_not_required(self):
        """Test that additional conditions (3-10) are marked as not required."""
        conditional_node = ConditionalNode()
        inputs = conditional_node.inputs

        # Check additional conditions (3-10)
        for i in range(3, 11):  # 3 to 10
            condition_source = next((inp for inp in inputs if inp.name == f"condition_{i}_source"), None)
            condition_operator = next((inp for inp in inputs if inp.name == f"condition_{i}_operator"), None)

            # Additional conditions should NOT be required to prevent start node collection
            assert condition_source is not None, f"condition_{i}_source should exist"
            assert condition_source.required is False, f"condition_{i}_source should not be required"
            assert condition_operator is not None, f"condition_{i}_operator should exist"
            assert condition_operator.required is False, f"condition_{i}_operator should not be required"

    def test_additional_conditions_have_visibility_rules(self):
        """Test that additional conditions have proper visibility rules."""
        conditional_node = ConditionalNode()
        inputs = conditional_node.inputs

        # Check that additional conditions have visibility rules
        for i in range(3, 11):  # 3 to 10
            condition_source = next((inp for inp in inputs if inp.name == f"condition_{i}_source"), None)
            condition_operator = next((inp for inp in inputs if inp.name == f"condition_{i}_operator"), None)

            assert condition_source is not None
            assert condition_source.visibility_rules is not None
            assert len(condition_source.visibility_rules) > 0

            assert condition_operator is not None
            assert condition_operator.visibility_rules is not None
            assert len(condition_operator.visibility_rules) > 0

    def test_conditional_node_input_structure(self):
        """Test the overall structure of conditional node inputs."""
        conditional_node = ConditionalNode()
        inputs = conditional_node.inputs

        # Count different types of inputs
        source_inputs = [inp for inp in inputs if inp.name.endswith("_source")]
        operator_inputs = [inp for inp in inputs if inp.name.endswith("_operator")]

        # Should have 10 source inputs and 10 operator inputs (conditions 1-10)
        assert len(source_inputs) == 10
        assert len(operator_inputs) == 10

        # Verify all condition numbers are present
        for i in range(1, 11):
            source_exists = any(inp.name == f"condition_{i}_source" for inp in inputs)
            operator_exists = any(inp.name == f"condition_{i}_operator" for inp in inputs)
            assert source_exists, f"condition_{i}_source should exist"
            assert operator_exists, f"condition_{i}_operator should exist"

    def test_dropdown_options_are_correct(self):
        """Test that dropdown inputs have the correct options."""
        conditional_node = ConditionalNode()
        inputs = conditional_node.inputs

        # Check source dropdown options
        for i in range(1, 11):
            condition_source = next((inp for inp in inputs if inp.name == f"condition_{i}_source"), None)
            assert condition_source is not None
            assert condition_source.options == ["node_output", "global_context"]

        # Check operator dropdown options
        expected_operators = [
            "equals", "not_equals", "contains", "starts_with", "ends_with",
            "greater_than", "less_than", "exists", "is_empty"
        ]
        for i in range(1, 11):
            condition_operator = next((inp for inp in inputs if inp.name == f"condition_{i}_operator"), None)
            assert condition_operator is not None
            assert condition_operator.options == expected_operators

    def test_default_values_are_set(self):
        """Test that dropdown inputs have appropriate default values."""
        conditional_node = ConditionalNode()
        inputs = conditional_node.inputs

        # Check default values
        for i in range(1, 11):
            condition_source = next((inp for inp in inputs if inp.name == f"condition_{i}_source"), None)
            condition_operator = next((inp for inp in inputs if inp.name == f"condition_{i}_operator"), None)

            assert condition_source is not None
            assert condition_source.value == "node_output"

            assert condition_operator is not None
            assert condition_operator.value == "equals"
