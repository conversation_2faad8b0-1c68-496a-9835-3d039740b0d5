"""
Simple script to run the table migration
"""

import os
import sys
from sqlalchemy import create_engine, text

def get_database_url():
    """Try to get database URL from environment or construct from parts"""
    # Try direct URL first
    db_url = os.getenv('DATABASE_URL') or os.getenv('SQLALCHEMY_DATABASE_URI')
    if db_url:
        return db_url
    
    # Try to construct from parts
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '5432')
    db_user = os.getenv('DB_USER', 'postgres')
    db_password = os.getenv('DB_PASSWORD', '')
    db_name = os.getenv('DB_NAME', 'workflow_db')
    
    return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

def copy_table_data(conn, old_table, new_table, table_type):
    """Copy data from old table to new table with available_nodes = []"""
    print(f"\n🔄 Copying {table_type} data from '{old_table}' to '{new_table}'...")
    
    # Check if old table exists
    result = conn.execute(text("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = :table_name
        );
    """), {"table_name": old_table})
    
    if not result.scalar():
        print(f"⚠️  Source table '{old_table}' does not exist. Skipping.")
        return
    
    # Check if new table exists
    result = conn.execute(text("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = :table_name
        );
    """), {"table_name": new_table})
    
    if not result.scalar():
        print(f"❌ Destination table '{new_table}' does not exist. Please create it first.")
        return
    
    # Get count of records
    count_result = conn.execute(text(f'SELECT COUNT(*) FROM "{old_table}"'))
    total_records = count_result.scalar()
    print(f"📊 Found {total_records} records to copy")
    
    if total_records == 0:
        print("ℹ️  No records to copy")
        return
    
    # Copy all data and add available_nodes = []
    copy_sql = f"""
        INSERT INTO "{new_table}" 
        SELECT *, '[]'::json as available_nodes 
        FROM "{old_table}"
        ON CONFLICT (id) DO NOTHING;
    """
    
    try:
        result = conn.execute(text(copy_sql))
        copied_count = result.rowcount
        print(f"✅ Successfully copied {copied_count} {table_type} records")
        
        if copied_count < total_records:
            print(f"ℹ️  {total_records - copied_count} records were skipped (already exist)")
            
    except Exception as e:
        print(f"❌ Error copying {table_type}: {str(e)}")
        raise

def main():
    print("🚀 Starting data migration: Copy rows with available_nodes = []")
    print("=" * 70)
    
    try:
        # Get database URL
        database_url = get_database_url()
        print(f"🔗 Connecting to database...")
        
        # Create engine and connect
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # Start transaction
            trans = conn.begin()
            
            try:
                # Copy workflows
                copy_table_data(
                    conn, 
                    "test-workflows", 
                    "test-workflows-available-nodes", 
                    "workflow"
                )
                
                # Copy templates
                copy_table_data(
                    conn, 
                    "test-workflow-templates", 
                    "test-workflow-templates-available-nodes", 
                    "template"
                )
                
                # Commit transaction
                trans.commit()
                
                print("\n" + "=" * 70)
                print("🎉 Migration completed successfully!")
                print("\nNext steps:")
                print("1. Update your table names in constants/table_names.py")
                print("2. Test your application")
                
            except Exception as e:
                trans.rollback()
                raise e
                
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        print("\nTry running with explicit database URL:")
        print("python scripts/copy_tables_with_available_nodes.py 'postgresql://user:pass@host:port/db'")
        sys.exit(1)

if __name__ == "__main__":
    main()
