import os
import grpc
from concurrent import futures
import structlog

from app.core.config import settings
from app.services.user_service import UserService
from app.grpc import user_pb2_grpc

logger = structlog.get_logger()

def serve():
    # Create gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    
    # Add user service to server
    user_service = UserService()
    user_pb2_grpc.add_UserServiceServicer_to_server(user_service, server)
    
    # Get port from environment or use default
    port = os.getenv('PORT', '50052')
    server.add_insecure_port(f'[::]:{port}')
    
    # Start server
    server.start()
    logger.info(f"User service started on port {port}")
    
    # Keep thread alive
    server.wait_for_termination()

if __name__ == '__main__':
    serve()