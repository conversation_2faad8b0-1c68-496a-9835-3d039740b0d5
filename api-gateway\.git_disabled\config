[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = *******************************:ruh.ai/api-gateway.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "ARW-424-migrate-component-endpoints-to-api-gateway"]
	remote = origin
	merge = refs/heads/ARW-424-migrate-component-endpoints-to-api-gateway
	vscode-merge-base = origin/main
[branch "dev"]
	remote = origin
	merge = refs/heads/dev
	vscode-merge-base = origin/dev
[branch "update-component-response"]
	vscode-merge-base = origin/dev
