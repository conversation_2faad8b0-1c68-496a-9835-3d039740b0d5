from typing import Optional
import grpc
from app.grpc import user_pb2, user_pb2_grpc
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.redis.redis_service import RedisService
from app.services.auth_haders import AuthService
from app.services.user_functions import UserFunctions
from app.services.waitlist_service import WaitlistService
from app.services.organization_service import OrganizationService
from app.services.api_key_service import APIKeyService
from app.services.credential_manager_service import CredentialService


class UserService(user_pb2_grpc.UserServiceServicer):
    def __init__(self):
        self.redis_service = RedisService()
        self.kafka_producer = KafkaProducer()
        self.auth_service = AuthService()
        self.user_functions = UserFunctions()
        self.waitlist_service = WaitlistService()
        self.organization_service = OrganizationService()
        self.api_key_service = APIKeyService()
        self.credential_service = CredentialService()

    def register(
        self, request: user_pb2.RegisterRequest, context: grpc.ServicerContext
    ) -> user_pb2.RegisterResponse:
        try:
            print("DEBUG: Starting registration process...")
            register_response = self.auth_service.register(request, context)
            print(f"DEBUG: Registration process completed. {register_response}")
            return register_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def login(
        self, request: user_pb2.LoginRequest, context: grpc.ServicerContext
    ) -> user_pb2.LoginResponse:
        try:
            login_response = self.auth_service.login(request, context)
            return login_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def googleOAuthLogin(
        self, request: user_pb2.GoogleOAuthRequest, context: grpc.ServicerContext
    ) -> user_pb2.LoginResponse:
        try:
            login_response = self.auth_service.googleOAuthLogin(request, context)
            return login_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def updateEmailVerifiedDetails(
        self, request: user_pb2.UpdateEmailVerificationDetails, context: grpc.ServicerContext
    ) -> user_pb2.UpdateEmailVerifiedDetailsResponse:
        try:
            update_email_verified_details_response = self.user_functions.updateEmailVerifiedDetails(
                request, context
            )
            return update_email_verified_details_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def accessToken(
        self, request: user_pb2.AccessTokenRequest, context: grpc.ServicerContext
    ) -> user_pb2.AccessTokenResponse:
        try:
            access_token_response = self.user_functions.accessToken(request, context)
            return access_token_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def generateResetPasswordOTP(
        self, request: user_pb2.ResetPasswordOTPRequest, context: grpc.ServicerContext
    ) -> user_pb2.ResetPasswordOTPResponse:
        try:
            reset_password_otp_response = self.user_functions.generateResetPasswordOTP(
                request, context
            )
            return reset_password_otp_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def updatePassword(
        self, request: user_pb2.UpdatePasswordRequest, context: grpc.ServicerContext
    ) -> user_pb2.UpdatePasswordResponse:
        try:
            update_password_response = self.user_functions.updatePassword(request, context)
            return update_password_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def resetPassword(
        self, request: user_pb2.ResetPasswordRequest, context: grpc.ServicerContext
    ) -> user_pb2.ResetPasswordResponse:
        try:
            reset_password_response = self.user_functions.resetPassword(request, context)
            return reset_password_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def getUser(
        self, request: user_pb2.GetUserRequest, context: grpc.ServicerContext
    ) -> user_pb2.UserResponse:
        try:
            user_response = self.user_functions.getUser(request, context)
            return user_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def getAllUsers(
        self, request: user_pb2.GetAllUsersRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetAllUsersResponse:
        try:
            all_users_response = self.user_functions.getAllUsers(request, context)
            return all_users_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def updateUser(
        self, request: user_pb2.UpdateUserRequest, context: grpc.ServicerContext
    ) -> user_pb2.UserResponse:
        try:
            update_user_response = self.user_functions.updateUser(request, context)
            return update_user_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def deleteUser(
        self, request: user_pb2.DeleteUserRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeleteUserResponse:
        try:
            delete_user_response = self.user_functions.deleteUser(request, context)
            return delete_user_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def listUsers(
        self, request: user_pb2.ListUsersRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListUsersResponse:
        try:
            list_users_response = self.user_functions.listUsers(request, context)
            return list_users_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def searchUsers(
        self, request: user_pb2.SearchUsersRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListUsersResponse:
        try:
            search_users_response = self.user_functions.searchUsers(request, context)
            return search_users_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def updateUserProfileDetails(
        self, request: user_pb2.UpdateUserProfileDetailsRequest, context: grpc.ServicerContext
    ) -> user_pb2.UserResponse:
        try:
            update_user_profile_details_response = self.user_functions.updateUserProfileDetails(
                request, context
            )
            return update_user_profile_details_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def updateStripeCustomerId(
        self, request: user_pb2.UpdateStripeCustomerIdRequest, context: grpc.ServicerContext
    ) -> user_pb2.UpdateStripeCustomerIdResponse:
        try:
            update_stripe_customer_id_response = self.user_functions.updateStripeCustomerId(
                request, context
            )
            return update_stripe_customer_id_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def fetchStripeCustomerId(
        self, request: user_pb2.FetchStripeCustomerIdRequest, context: grpc.ServicerContext
    ) -> user_pb2.FetchStripeCustomerIdResponse:
        try:
            fetch_stripe_customer_id_response = self.user_functions.fetchStripeCustomerId(
                request, context
            )
            return fetch_stripe_customer_id_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def validateUser(
        self, request: user_pb2.ValidateUserRequest, context: grpc.ServicerContext
    ) -> user_pb2.ValidateUserResponse:
        try:
            validate_user_response = self.user_functions.validateUser(request, context)
            return validate_user_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def createOrganization(
        self, request: user_pb2.CreateOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.OrganizationResponse:
        try:
            Organization = self.organization_service.createOrganization(request, context)

            return Organization
        except Exception as e:

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def getOrganization(
        self, request: user_pb2.GetOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.OrganizationResponse:
        try:
            Organization = self.organization_service.getOrganization(request, context)

            return Organization
        except Exception as e:

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def updateOrganization(
        self, request: user_pb2.UpdateOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.OrganizationResponse:
        try:
            Organization = self.organization_service.updateOrganization(request, context)

            return Organization
        except Exception as e:

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def deleteOrganization(
        self, request: user_pb2.DeleteOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeleteOrganizationResponse:
        try:
            Organization = self.organization_service.deleteOrganization(request, context)

            return Organization
        except Exception as e:

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def addUserToOrganization(
        self, request: user_pb2.AddUserToOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.OrganizationResponse:
        try:
            Organization = self.organization_service.addUserToOrganization(request, context)

            return Organization
        except Exception as e:

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def removeUserFromOrganization(
        self, request: user_pb2.RemoveUserFromOrganizationRequest, context: grpc.ServicerContext
    ) -> user_pb2.OrganizationResponse:
        try:
            Organization = self.organization_service.removeUserFromOrganization(request, context)

            return Organization
        except Exception as e:

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def listUserOrganizations(
        self, request: user_pb2.ListUserOrganizationsRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListOrganizationsResponse:
        try:
            Organization = self.organization_service.listUserOrganizations(request, context)

            return Organization
        except Exception as e:

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def addToWaitlist(
        self, request: user_pb2.AddToWaitlistRequest, context: grpc.ServicerContext
    ) -> user_pb2.AddToWaitlistResponse:
        try:
            add_to_waitlist_response = self.waitlist_service.addToWaitlist(request, context)
            return add_to_waitlist_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def getWaitlist(
        self, request: user_pb2.GetWaitlistRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetWaitlistResponse:
        try:
            get_waitlist_response = self.waitlist_service.getWaitlist(request, context)
            return get_waitlist_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def approveWaitlistUser(
        self, request: user_pb2.ApproveWaitlistUserRequest, context: grpc.ServicerContext
    ) -> user_pb2.ApproveWaitlistUserResponse:
        try:
            approve_waitlist_user_response = self.waitlist_service.approveWaitlistUser(
                request, context
            )
            return approve_waitlist_user_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def approveMultipleWaitlistUsers(
        self, request: user_pb2.ApproveMultipleWaitlistUsersRequest, context: grpc.ServicerContext
    ) -> user_pb2.ApproveMultipleWaitlistUsersResponse:
        try:
            approve_multiple_waitlist_users_response = (
                self.waitlist_service.approveMultipleWaitlistUsers(request, context)
            )
            return approve_multiple_waitlist_users_response
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def GenerateAPIKey(
        self, request: user_pb2.GenerateAPIKeyRequest, context: grpc.ServicerContext
    ) -> user_pb2.GenerateAPIKeyResponse:
        try:
            result = self.api_key_service.generate_api_key(request, context)
            return result
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def ListAPIKeys(
        self, request: user_pb2.ListAPIKeysRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListAPIKeysResponse:
        try:
            result = self.api_key_service.list_api_keys(request, context)
            return result
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def DeleteAPIKey(
        self, request: user_pb2.DeleteAPIKeyRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeleteAPIKeyResponse:
        try:
            result = self.api_key_service.delete_api_key(request, context)
            return result
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def GetAPIKeyById(
        self, request: user_pb2.GetAPIKeyByIdRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetAPIKeyByIdResponse:
        try:
            response = self.api_key_service.get_api_key_by_id(request, context)
            return response
        except Exception as e:
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def CreateCredential(
        self, request: user_pb2.CreateCredentialRequest, context: grpc.ServicerContext
    ) -> user_pb2.CreateCredentialResponse:
        try:
            result = self.credential_service.create_credential(request, context)
            return result
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def GetCredential(
        self, request: user_pb2.GetCredentialRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetCredentialResponse:
        try:
            result = self.credential_service.get_credential(request, context)
            return result
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def ListCredentials(
        self, request: user_pb2.ListCredentialsRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListCredentialsResponse:
        try:
            print("DEBUG: Starting list_credentials")
            result = self.credential_service.list_credentials(request, context)
            return result
        except Exception as e:
            # Raise error
            print(f"DEBUG: Error in list_credentials: {str(e)}")
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def DeleteCredential(
        self, request: user_pb2.DeleteCredentialRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeleteCredentialResponse:
        try:
            result = self.credential_service.delete_credential(request, context)
            return result
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def UpdateCredential(
        self, request: user_pb2.UpdateCredentialRequest, context: grpc.ServicerContext
    ) -> user_pb2.UpdateCredentialResponse:
        try:
            result = self.credential_service.update_credential(request, context)
            return result
        except Exception as e:
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    def getUsersByIds(
        self, request: user_pb2.GetUsersByIdsRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetUsersByIdsResponse:
        """
        Delegates the getUsersByIds request to the UserFunctions implementation.

        Args:
            request (user_pb2.GetUsersByIdsRequest): Contains a list of user IDs to retrieve
            context (grpc.ServicerContext): gRPC context for error handling

        Returns:
            user_pb2.GetUsersByIdsResponse: Contains a list of UserDetails objects
        """
        try:
            print(f"[INFO] Received request to get users by IDs: {len(request.user_ids)} IDs")
            response = self.user_functions.getUsersByIds(request, context)
            return response
        except Exception as e:
            print(f"[ERROR] Failed to retrieve users by IDs: {str(e)}")
            # Raise error
            raise grpc.RpcError(grpc.StatusCode.INTERNAL, f"Failed to retrieve users: {str(e)}")

    def updateUserGitToken(
        self, request: user_pb2.UpdateUserGitToken, context: grpc.ServicerContext
    ) -> user_pb2.UserResponse:
        try:
            print(f"[INFO] Received request for updateUserGitToken")
            response = self.user_functions.updateUserGitToken(request, context)
            return response
        except Exception as e:
            print(f"[ERROR] Failed to update git token for user: {str(e)}")
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to update git token for user: {str(e)}"
            )

    def getUserGitHubToken(
        self, request: user_pb2.GetUserGitHubTokenRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetUserGitHubTokenResponse:
        try:
            print(f"[INFO] Received request for getUserGitHubToken")
            response = self.user_functions.getUserGitHubToken(request, context)
            return response
        except Exception as e:
            print(f"[ERROR] Failed to get GitHub token for user: {str(e)}")
            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to get GitHub token for user: {str(e)}"
            )
