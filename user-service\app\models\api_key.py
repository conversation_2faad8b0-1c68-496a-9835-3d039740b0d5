from datetime import datetime
from sqlalchemy import Column, ForeignKey, String, DateTime
from sqlalchemy.orm import relationship
from app.models.user import Base

class APIKey(Base):
    __tablename__ = "api_keys"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    public_key = Column(String, unique=True, nullable=False)
    private_key = Column(String, nullable=False)  # Will be encrypted
    project = Column(String, nullable=True)
    user_id = Column(String, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationship with User
    user = relationship("User", backref="api_keys")

    def __repr__(self):
        return f"<APIKey {self.name}>"