# pack-refs with: peeled fully-peeled sorted 
f196211b1f4b5e2a3291bbe8b24d7630de5f0389 refs/remotes/origin/ARW-109-develop-communication-microservice
8c1921bebe26dc6fd1e66e13eec5295339a5e77f refs/remotes/origin/ARW-122-implement-endpoint-for-google-and-update-password-in-api-gateway
bcce9f198dcad8f3ead1022758f8a194e8d9eb8e refs/remotes/origin/ARW-125-create-apis-in-api-gateway-for-workflow-builder-service
667d94585d891fbfbcb4dadca752a7a5d332afac refs/remotes/origin/ARW-126-create-apis-in-api-gateway-for-agent-builder-service
29f0b91d91efeabc7827077ec5e230da9a657f89 refs/remotes/origin/ARW-132-create-an-api-gateway-endpoint-for-kafka
438ba7728a3e5a8e7d3b02891e90436d0c6cc716 refs/remotes/origin/ARW-179-update-api-gateway-to-handle-new-kafka-changes
7ceab627b2c8ea2c20b87114e05e03e7ba6d67c6 refs/remotes/origin/ARW-197-update-communication-service-remove-agent-id-and-store-g-rpc-enums-as-strings
85bea0f4ef41723e80c626bd480c4e2fa3d9477c refs/remotes/origin/ARW-220-create-apis-in-api-gateway-for-mcp-server-registry-microservice
038c6dbf5f7b710b1be7c02cd25f8e67032badd9 refs/remotes/origin/ARW-232-create-apis-in-api-gateway-for-organization-management-within-user-microservice
6f97a7744544487291f3e4a6c2f9f7556f088c5b refs/remotes/origin/ARW-246-connect-orchestration-workflow-with-the-ciny-api-gateway-also-handle-the-events
26631d6e5707f54b2e813ecfe559748ee646e51c refs/remotes/origin/ARW-27-implement-authorization-middleware
d01146e535bf2d42d3fc205f30b384e5ad079d34 refs/remotes/origin/ARW-28-user-listing-pagination-sorting-filtering-and-search
d752b23d14a4b9725daf5e77aec1174a4dba5d96 refs/remotes/origin/ARW-281-create-a-endpoint-in-api-gateway-to-take-profile-details-from-user
5c07aa848d5e675223621f1098418601083b02d7 refs/remotes/origin/ARW-291-write-a-apis-in-api-gateway-to-add-manage-users-in-waitlist
1a2ba28936a41fd36cad8f1755861ac1aff317ee refs/remotes/origin/ARW-295-generate-ruh-api-service-keys-and-authenticate-workflow-using-key
3f8a92089bd62043cac6173a3ded06e1b97dacfe refs/remotes/origin/ARW-298-api-gateway-add-rest-ap-is-and-g-rpc-client-for-stripe-customer-id
6a0f3037d050a0820b91fc1f6f55f6cd7fe1c64a refs/remotes/origin/ARW-299-add-stripe-customer-id-field-and-g-rpc-endpoints-in-user-microservice
c928fe4cd67886e664d4b45de15bd753c5c5d2ee refs/remotes/origin/ARW-314-implement-api-to-take-fcm-token-from-frontend
aafbe92385f506c14852a73287d4182bbb065f5b refs/remotes/origin/ARW-328-add-mcp-execution-service-endpoints
f512f6a17b0462a519b95bc364f69b446f7b1c3d refs/remotes/origin/ARW-351-integrate-live-kit-backend-with-ruh-agent-frontend-huddle-button-for-voice-chat-text-chat
b2f456972e7c704632baa62a12483e21ea8d7497 refs/remotes/origin/ARW-369-implement-api-key-management-endpoints-in-api-gateway
e1c62420fc15c3b8bd5bb83c591db92293ef5446 refs/remotes/origin/ARW-373-update-api-endpoints-according-to-new-agent-schema-and-grpc-methods
04995c693209090afd8f158f696e6934dc77711e refs/remotes/origin/ARW-378-update-endpoints-wrt-workflow-schema-workflow-template
c074cc6345a82b2f6a906e9eb2b17aade577c5d7 refs/remotes/origin/ARW-385-update-endpoints-in-api-gateway-for-mcp-management-wrt-mcp-service
05dd74f37a909a7bf9e8f4c06253d1f6f5dde058 refs/remotes/origin/ARW-387-implement-credential-management-system-in-user-service
91b609fa8bef7b6be7643b4b89dc7539dd72873a refs/remotes/origin/ARW-391-remove-workflow-execution-routes-sse-routes-from-api-gateway
a8b37e17e11e2c5c839aafebf82bccf824eb15b6 refs/remotes/origin/ARW-393-enhance-agent-and-template-endpoints-with-department-tone-files-and-urls-fields
bc16b073d9a4834ffa3761803d286082df3ed516 refs/remotes/origin/ARW-408-add-update-the-existing-routes-for-user-info-retrieval-in-api-gateway
99440667c935f6b3959b5c18ebb4970d085fd664 refs/remotes/origin/ARW-415-create-update-the-agent-ap-is-according-to-marketplace
bd22f8a19ba0c351fb0508286256b3fbb9b37742 refs/remotes/origin/ARW-418-create-update-workflow-apis-for-marketplace
91757935a36202928a8fe41de8add5ab4e5f014a refs/remotes/origin/ARW-424-migrate-component-endpoints-to-api-gateway
5bb365952a1404a187bef587d0ac456375814b62 refs/remotes/origin/ARW-435-add-health-check-service-into-api-gateway-to-check-health-of-services
4523a1514a40562dc6c2a6d3cc67dca1e0d87826 refs/remotes/origin/dev
a0c3bd1e9f6a87fd72d0cb7561b21eb1eca6d361 refs/remotes/origin/enhanced-and-refactored-workflow-routes
760b38805be64b588ddd4780f4c84f07fc9bdf66 refs/remotes/origin/improvements
66333cceb7e837ad5a10ee60f2a336cea9c5193d refs/remotes/origin/main
a60fad41dbe2dd8d6313bbcddf5dcc4a6f7938fa refs/remotes/origin/notification-routes
581278a91dae0b6cd4c2f1f49fb8c7a85a52f24f refs/remotes/origin/organization-waitlist-management-routes
389bcb4ee438bc3a0f29b6884dce4fa01ad72945 refs/remotes/origin/test-version-issue
