// src/app/api/auth.ts
import { authApi as centralizedAuth<PERSON><PERSON> } from "@/utils/axios";
import { setAccessToken, setRefreshToken, clearAuthCookies } from "@/utils/authCookies";
import { LoginType, SignupType as RegisterType, ResetPasswordType } from "@/lib/schemas/auth";
import { API_ENDPOINTS } from "@/lib/apiConfig";

// Auth API interface
export const authApi = {
  /**
   * Login user with email and password
   * @param data LoginType containing email and password
   * @returns User data and tokens
   */
  login: async (data: LoginType) => {
    try {
      const response = await centralizedAuthApi.post(API_ENDPOINTS.AUTH.LOGIN, data);

      // Set tokens in cookies
      if (response.data.access_token) {
        setAccessToken(response.data.access_token);
      }

      if (response.data.refresh_token) {
        setRefreshToken(response.data.refresh_token);
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Login failed",
      );
    }
  },

  /**
   * Register a new user
   * @param data RegisterType containing user registration data
   * @returns Success message
   */
  register: async (data: RegisterType) => {
    try {
      const response = await centralizedAuthApi.post(API_ENDPOINTS.AUTH.REGISTER, data);
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Registration failed",
      );
    }
  },

  /**
   * Logout user
   * @returns Success message
   */
  logout: async () => {
    try {
      const response = await centralizedAuthApi.post(API_ENDPOINTS.AUTH.LOGOUT);
      await clearAuthCookies();
      return response.data;
    } catch (error: any) {
      // Clear cookies even if the API call fails
      await clearAuthCookies();
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Logout failed",
      );
    }
  },

  /**
   * Generate a new access token using refresh token
   * @param refreshToken The refresh token
   * @returns New access token
   */
  generateAccessToken: async (refreshToken: string) => {
    try {
      const response = await centralizedAuthApi.post(API_ENDPOINTS.AUTH.REFRESH, {
        refresh_token: refreshToken,
      });

      if (response.data.access_token) {
        setAccessToken(response.data.access_token);
      }

      return {
        success: true,
        access_token: response.data.access_token,
      };
    } catch (error: any) {
      return {
        success: false,
        error:
          error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to refresh token",
      };
    }
  },

  /**
   * Send password reset email
   * @param email User's email address
   * @returns Success message
   */
  forgotPassword: async (email: string) => {
    try {
      const response = await centralizedAuthApi.post(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, { email });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to send reset email",
      );
    }
  },

  /**
   * Reset user's password using token
   * @param token The reset token from the email
   * @param data ResetPasswordType containing new password
   * @returns Success message
   */
  resetPassword: async (token: string, data: ResetPasswordType) => {
    try {
      const response = await centralizedAuthApi.post(API_ENDPOINTS.AUTH.RESET_PASSWORD, {
        token,
        new_password: data.newPassword,
        confirm_password: data.confirmNewPassword,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || error.response?.data?.message || "Password reset failed",
      );
    }
  },

  /**
   * Verify user's email
   * @param token The verification token from the email
   * @returns Success message
   */
  verifyEmail: async (token: string) => {
    try {
      const response = await centralizedAuthApi.post(API_ENDPOINTS.AUTH.VERIFY_EMAIL, { token });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Email verification failed",
      );
    }
  },

  /**
   * Verify user's email with OTP
   * @param token The OTP token from the email
   * @returns Success message
   */
  verifyEmailOtp: async (token: string) => {
    try {
      const response = await centralizedAuthApi.post(API_ENDPOINTS.AUTH.VERIFY_EMAIL_OTP, {
        token,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Email verification failed",
      );
    }
  },
};
