#!/usr/bin/env python3
"""
Test script to verify that API routes are properly aligned with proto definitions.
This script validates that all proto service methods have corresponding REST endpoints
and that the request/response models match the proto message definitions.
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_analytics_proto_alignment():
    """Test that analytics routes align with proto definitions."""
    print("🧪 Testing Analytics Proto Alignment...")

    try:
        from app.schemas.analytics import (
            # Enums should match proto exactly
            EventTypeEnum,
            ServiceTypeEnum,
            ApplicationStatusEnum,
            WebhookStatusEnum,
            WebhookEventTypeEnum,
            ActivationEventTypeEnum,
            RequestTypeEnum,
            RequestStatusEnum,
            CreditCategoryEnum,
            # Models should match proto messages
            TrackEventRequest,
            TrackEventResponse,
            ServiceMetricsResponse,
            UserActivityResponse,
            RatingAnalyticsResponse,
            OverviewAnalyticsResponse,
            TrackActivationRequest,
            TrackActivationResponse,
            ActivationMetricsResponse,
            CreateWebhookRequest,
            UpdateWebhookRequest,
            Webhook,
            WebhookResponse,
            GetWebhooksResponse,
            WebhookLog,
            GetWebhookLogsResponse,
            DashboardOverviewResponse,
            DashboardMetricsResponse,
            CreditUsageBreakdownResponse,
            AppCreditUsageResponse,
            LatestApiRequestsResponse,
            AgentPerformanceResponse,
            WorkflowUtilizationResponse,
            SystemActivityResponse,
            RecordApiRequestRequest,
            RecordApiRequestResponse,
            RecordSystemActivityRequest,
            RecordSystemActivityResponse,
        )

        # Test enum values match proto
        expected_event_types = [
            "unspecified",
            "usage",
            "rating",
            "creation",
            "modification",
            "deletion",
            "login",
            "logout",
            "error",
            "other",
        ]
        actual_event_types = [e.value for e in EventTypeEnum]

        for expected in expected_event_types:
            if expected not in actual_event_types:
                print(f"❌ Missing EventType: {expected}")
                return False

        expected_service_types = [
            "unspecified",
            "mcp",
            "workflow",
            "agent",
            "user",
            "application",
            "webhook",
            "other",
        ]
        actual_service_types = [e.value for e in ServiceTypeEnum]

        for expected in expected_service_types:
            if expected not in actual_service_types:
                print(f"❌ Missing ServiceType: {expected}")
                return False

        # Test that webhook model has all proto fields
        webhook_fields = Webhook.__fields__.keys()
        expected_webhook_fields = [
            "id",
            "user_id",
            "application_id",
            "url",
            "event_types",
            "description",
            "is_active",
            "status",
            "created_at",
            "updated_at",
            "delivery_count",
            "failure_count",
            "last_delivery_at",
        ]

        for field in expected_webhook_fields:
            if field not in webhook_fields:
                print(f"❌ Missing Webhook field: {field}")
                return False

        # Test webhook log model
        webhook_log_fields = WebhookLog.__fields__.keys()
        expected_log_fields = [
            "id",
            "webhook_id",
            "event_type",
            "payload",
            "response_status",
            "response_body",
            "error_message",
            "delivered_at",
            "success",
            "retry_count",
        ]

        for field in expected_log_fields:
            if field not in webhook_log_fields:
                print(f"❌ Missing WebhookLog field: {field}")
                return False

        print("✅ Analytics proto alignment verified")
        return True

    except Exception as e:
        print(f"❌ Analytics proto alignment failed: {e}")
        return False


def test_application_proto_alignment():
    """Test that application routes align with proto definitions."""
    print("\n🧪 Testing Application Proto Alignment...")

    try:
        from app.schemas.application import (
            # Proto-aligned models
            CreateApplicationRequest,
            CreateApplicationResponse,
            GetApplicationsRequest,
            GetApplicationsResponse,
            GetApplicationRequest,
            GetApplicationResponse,
            UpdateApplicationRequest,
            UpdateApplicationResponse,
            DeleteApplicationRequest,
            DeleteApplicationResponse,
            AttachImageToApplicationRequest,
            AttachImageToApplicationResponse,
            Application,
            ApplicationMetrics,
            ApplicationStatusEnum,
        )

        # Test Application model has all proto fields
        app_fields = Application.__fields__.keys()
        expected_app_fields = [
            "id",
            "user_id",
            "name",
            "description",
            "workflow_ids",
            "agent_ids",
            "status",
            "created_at",
            "updated_at",
            "api_keys",
            "is_deleted",
        ]

        for field in expected_app_fields:
            if field not in app_fields:
                print(f"❌ Missing Application field: {field}")
                return False

        # Test ApplicationMetrics model
        metrics_fields = ApplicationMetrics.__fields__.keys()
        expected_metrics_fields = [
            "application_id",
            "total_requests",
            "successful_requests",
            "failed_requests",
            "credits_used",
            "last_request_at",
            "usage_trend",
        ]

        for field in expected_metrics_fields:
            if field not in metrics_fields:
                print(f"❌ Missing ApplicationMetrics field: {field}")
                return False

        # Test status enum
        expected_statuses = ["unspecified", "active", "inactive", "suspended"]
        actual_statuses = [e.value for e in ApplicationStatusEnum]

        for expected in expected_statuses:
            if expected not in actual_statuses:
                print(f"❌ Missing ApplicationStatus: {expected}")
                return False

        # Test CreateApplicationRequest fields
        create_fields = CreateApplicationRequest.__fields__.keys()
        expected_create_fields = [
            "user_id",
            "name",
            "description",
            "workflow_ids",
            "agent_ids",
            "alias",
            "api_keys",
        ]

        for field in expected_create_fields:
            if field not in create_fields:
                print(f"❌ Missing CreateApplicationRequest field: {field}")
                return False

        print("✅ Application proto alignment verified")
        return True

    except Exception as e:
        print(f"❌ Application proto alignment failed: {e}")
        return False


def test_route_coverage():
    """Test that all proto service methods have corresponding REST endpoints."""
    print("\n🧪 Testing Route Coverage...")

    try:
        import ast

        # Check analytics routes
        with open("app/api/routers/analytics_routes.py", "r") as f:
            analytics_code = f.read()

        analytics_tree = ast.parse(analytics_code)
        analytics_functions = [
            node.name
            for node in ast.walk(analytics_tree)
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))
        ]

        # Expected analytics endpoints based on proto service methods
        expected_analytics_endpoints = [
            "track_event",
            "get_service_metrics",
            "get_user_activity",
            "get_rating_analytics",
            "get_overview_analytics",
            "track_activation",
            "get_activation_metrics",
            "create_webhook",
            "get_webhooks",
            "update_webhook",
            "delete_webhook",
            "get_webhook_logs",
            "get_dashboard_overview",
            "get_dashboard_metrics",
            "get_credit_usage_breakdown",
            "get_app_credit_usage",
            "get_latest_api_requests",
            "get_agent_performance",
            "get_workflow_utilization",
            "get_system_activity",
            "record_api_request",
            "record_system_activity",
        ]

        missing_analytics = []
        for endpoint in expected_analytics_endpoints:
            if endpoint not in analytics_functions:
                missing_analytics.append(endpoint)

        if missing_analytics:
            print(f"❌ Missing analytics endpoints: {missing_analytics}")
            return False

        # Check application routes
        with open("app/api/routers/application_routes.py", "r") as f:
            application_code = f.read()

        application_tree = ast.parse(application_code)
        application_functions = [
            node.name
            for node in ast.walk(application_tree)
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))
        ]

        # Expected application endpoints based on proto service methods
        expected_application_endpoints = [
            "create_application",
            "get_applications",
            "get_application",
            "update_application",
            "delete_application",
            "attach_image_to_application",
        ]

        missing_application = []
        for endpoint in expected_application_endpoints:
            if endpoint not in application_functions:
                missing_application.append(endpoint)

        if missing_application:
            print(f"❌ Missing application endpoints: {missing_application}")
            return False

        print(f"✅ Found {len(analytics_functions)} analytics endpoints")
        print(f"✅ Found {len(application_functions)} application endpoints")
        print("✅ All proto service methods have corresponding REST endpoints")
        return True

    except Exception as e:
        print(f"❌ Route coverage test failed: {e}")
        return False


def test_request_response_models():
    """Test that request/response models are properly structured."""
    print("\n🧪 Testing Request/Response Models...")

    try:
        from app.schemas.analytics import TrackEventRequest, TrackEventResponse
        from app.schemas.application import CreateApplicationRequest, CreateApplicationResponse

        # Test TrackEventRequest matches proto
        track_event_fields = TrackEventRequest.__fields__.keys()
        expected_fields = ["event_type", "service_type", "entity_id", "user_id", "metadata"]

        for field in expected_fields:
            if field not in track_event_fields:
                print(f"❌ Missing TrackEventRequest field: {field}")
                return False

        # Test response models have success/message pattern
        track_response_fields = TrackEventResponse.__fields__.keys()
        if "success" not in track_response_fields or "message" not in track_response_fields:
            print("❌ TrackEventResponse missing success/message fields")
            return False

        create_response_fields = CreateApplicationResponse.__fields__.keys()
        if "success" not in create_response_fields or "message" not in create_response_fields:
            print("❌ CreateApplicationResponse missing success/message fields")
            return False

        print("✅ Request/response models properly structured")
        return True

    except Exception as e:
        print(f"❌ Request/response model test failed: {e}")
        return False


def main():
    """Run all proto alignment tests."""
    print("🚀 Starting Proto Alignment Tests...\n")

    tests = [
        test_analytics_proto_alignment,
        test_application_proto_alignment,
        test_route_coverage,
        test_request_response_models,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All proto alignment tests passed! Routes are properly aligned.")
        return True
    else:
        print("❌ Some proto alignment tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
