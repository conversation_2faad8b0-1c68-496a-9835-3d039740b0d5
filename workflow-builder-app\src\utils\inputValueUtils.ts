import { Node } from "reactflow";
import { WorkflowNodeData, InputDefinition } from "@/types";
import { useComponentStateStore } from "@/store/mcpToolsStore";

/**
 * Get the effective value for an input field with proper fallback logic
 * This function handles the value resolution order for all component types:
 * 1. Component state (for MCP components)
 * 2. Node config value (user-configured value)
 * 3. Input definition default value
 * 4. Type-appropriate fallback
 * 
 * @param node The workflow node
 * @param inputDef The input definition
 * @returns The effective value to display in the input field
 */
export function getEffectiveInputValue(
  node: Node<WorkflowNodeData>, 
  inputDef: InputDefinition
): any {
  // Special handling for MCP components
  if (node.data.type === "mcp") {
    const stateStore = useComponentStateStore.getState();
    const config = stateStore.getValue(node.id, "config", {});
    if (config[inputDef.name] !== undefined) {
      return config[inputDef.name];
    }
  }

  // Try to get from the node config (user-configured value)
  const configValue = node.data.config?.[inputDef.name];
  if (configValue !== undefined) {
    return configValue;
  }

  // Fall back to the input definition's default value
  if (inputDef.value !== undefined) {
    return inputDef.value;
  }

  // Final fallback based on input type
  return getTypeFallbackValue(inputDef.input_type);
}

/**
 * Get a type-appropriate fallback value when no other value is available
 * @param inputType The input type
 * @returns A sensible default value for the input type
 */
function getTypeFallbackValue(inputType: string): any {
  switch (inputType) {
    case "bool":
      return false;
    case "int":
    case "float":
      return 0;
    case "dropdown":
      return "";
    case "string":
    case "password":
    case "multiline":
    case "code":
    default:
      return "";
  }
}

/**
 * Check if a value is considered "empty" or "default" for display purposes
 * @param value The value to check
 * @param inputDef The input definition
 * @returns True if the value should be considered empty/default
 */
export function isValueEmpty(value: any, inputDef: InputDefinition): boolean {
  if (value === undefined || value === null) {
    return true;
  }

  switch (inputDef.input_type) {
    case "bool":
      return false; // Boolean values are never considered empty
    case "int":
    case "float":
      return value === 0 && inputDef.value !== 0; // Only empty if 0 and default is not 0
    case "string":
    case "password":
    case "multiline":
    case "code":
    case "dropdown":
    default:
      return value === "";
  }
}

/**
 * Check if the current value is different from the default value
 * @param node The workflow node
 * @param inputDef The input definition
 * @returns True if the value has been modified from default
 */
export function isValueModified(
  node: Node<WorkflowNodeData>, 
  inputDef: InputDefinition
): boolean {
  const currentValue = getEffectiveInputValue(node, inputDef);
  const defaultValue = inputDef.value ?? getTypeFallbackValue(inputDef.input_type);
  
  return JSON.stringify(currentValue) !== JSON.stringify(defaultValue);
}
