"""
Application Service Client

This module provides a client for communicating with the Application Service via gRPC.
It handles all application management operations including CRUD operations and image attachments.
"""

import logging
from typing import Dict, List, Optional, Any
import grpc
from grpc import RpcError

from app.core.config import settings
from app.grpc_ import analytics_pb2, analytics_pb2_grpc

logger = logging.getLogger(__name__)


class ApplicationServiceClient:
    """Client for Application Service gRPC communication."""

    def __init__(self):
        """Initialize the Application Service client."""
        self.channel = None
        self.application_stub = None
        self._connect()

    def _connect(self):
        """Establish gRPC connection to Application Service."""
        try:
            analytics_address = (
                f"{settings.ANALYTICS_SERVICE_HOST}:{settings.ANALYTICS_SERVICE_PORT}"
            )
            self.channel = grpc.insecure_channel(analytics_address)
            self.application_stub = analytics_pb2_grpc.ApplicationServiceStub(self.channel)
            logger.info(f"Connected to Application Service at {analytics_address}")
        except Exception as e:
            logger.error(f"Failed to connect to Application Service: {e}")
            raise

    def close(self):
        """Close the gRPC connection."""
        if self.channel:
            self.channel.close()

    # ===== APPLICATION MANAGEMENT =====

    async def create_application(
        self,
        user_id: str,
        name: str,
        description: str,
        workflow_ids: Optional[List[str]] = None,
        agent_ids: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Create a new application.

        Args:
            user_id: ID of the user
            name: Application name
            description: Application description
            workflow_ids: Optional list of workflow IDs
            agent_ids: Optional list of agent IDs

        Returns:
            Dict containing application data
        """
        try:
            request = analytics_pb2.CreateApplicationRequest(
                user_id=user_id,
                name=name,
                description=description,
                workflow_ids=workflow_ids or [],
                agent_ids=agent_ids or [],
            )

            response = self.application_stub.CreateApplication(request)

            application = response.application
            return {
                "success": response.success,
                "message": response.message,
                "application": {
                    "id": application.id,
                    "user_id": application.user_id,
                    "name": application.name,
                    "description": application.description,
                    "workflow_ids": list(application.workflow_ids),
                    "agent_ids": list(application.agent_ids),
                    "status": application.status,
                    "created_at": application.created_at,
                    "updated_at": application.updated_at,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error creating application: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "application": None}
        except Exception as e:
            logger.error(f"Error creating application: {e}")
            return {"success": False, "message": str(e), "application": None}

    async def get_applications(
        self, user_id: str, status: Optional[str] = None, limit: int = 50, offset: int = 0
    ) -> Dict[str, Any]:
        """
        Get applications for a user.

        Args:
            user_id: ID of the user
            status: Optional status filter
            limit: Maximum number of results
            offset: Offset for pagination

        Returns:
            Dict containing list of applications
        """
        try:
            status_enum = analytics_pb2.ApplicationStatus.APPLICATION_STATUS_UNSPECIFIED
            if status:
                status_enum = getattr(
                    analytics_pb2.ApplicationStatus,
                    f"APPLICATION_STATUS_{status.upper()}",
                    analytics_pb2.ApplicationStatus.APPLICATION_STATUS_UNSPECIFIED,
                )

            request = analytics_pb2.GetApplicationsRequest(
                user_id=user_id, status=status_enum, limit=limit, offset=offset
            )

            response = self.application_stub.GetApplications(request)

            applications = [
                {
                    "id": app.id,
                    "user_id": app.user_id,
                    "name": app.name,
                    "description": app.description,
                    "workflow_ids": list(app.workflow_ids),
                    "agent_ids": list(app.agent_ids),
                    "status": app.status,
                    "created_at": app.created_at,
                    "updated_at": app.updated_at,
                }
                for app in response.applications
            ]

            return {
                "success": response.success,
                "message": response.message,
                "applications": applications,
                "total_count": response.total_count,
            }

        except RpcError as e:
            logger.error(f"gRPC error getting applications: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {e.details()}",
                "applications": [],
                "total_count": 0,
            }
        except Exception as e:
            logger.error(f"Error getting applications: {e}")
            return {"success": False, "message": str(e), "applications": [], "total_count": 0}

    async def get_application(self, application_id: str, user_id: str) -> Dict[str, Any]:
        """
        Get a specific application.

        Args:
            application_id: ID of the application
            user_id: ID of the user

        Returns:
            Dict containing application details and metrics
        """
        try:
            request = analytics_pb2.GetApplicationRequest(
                application_id=application_id, user_id=user_id
            )

            response = self.application_stub.GetApplication(request)

            application = response.application
            metrics = response.metrics

            # Convert usage trend
            usage_trend = [
                {"date": point.date, "count": point.count} for point in metrics.usage_trend
            ]

            return {
                "success": response.success,
                "message": response.message,
                "application": {
                    "id": application.id,
                    "user_id": application.user_id,
                    "name": application.name,
                    "description": application.description,
                    "workflow_ids": list(application.workflow_ids),
                    "agent_ids": list(application.agent_ids),
                    "status": application.status,
                    "created_at": application.created_at,
                    "updated_at": application.updated_at,
                },
                "metrics": {
                    "application_id": metrics.application_id,
                    "total_requests": metrics.total_requests,
                    "successful_requests": metrics.successful_requests,
                    "failed_requests": metrics.failed_requests,
                    "credits_used": metrics.credits_used,
                    "last_request_at": metrics.last_request_at,
                    "usage_trend": usage_trend,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error getting application: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {e.details()}",
                "application": None,
                "metrics": None,
            }
        except Exception as e:
            logger.error(f"Error getting application: {e}")
            return {"success": False, "message": str(e), "application": None, "metrics": None}

    async def update_application(
        self,
        application_id: str,
        user_id: str,
        name: str,
        description: str,
        workflow_ids: Optional[List[str]] = None,
        agent_ids: Optional[List[str]] = None,
        status: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Update an application.

        Args:
            application_id: ID of the application
            user_id: ID of the user
            name: Application name
            description: Application description
            workflow_ids: Optional list of workflow IDs
            agent_ids: Optional list of agent IDs
            status: Optional status

        Returns:
            Dict containing updated application data
        """
        try:
            status_enum = analytics_pb2.ApplicationStatus.APPLICATION_STATUS_UNSPECIFIED
            if status:
                status_enum = getattr(
                    analytics_pb2.ApplicationStatus,
                    f"APPLICATION_STATUS_{status.upper()}",
                    analytics_pb2.ApplicationStatus.APPLICATION_STATUS_UNSPECIFIED,
                )

            request = analytics_pb2.UpdateApplicationRequest(
                application_id=application_id,
                user_id=user_id,
                name=name,
                description=description,
                workflow_ids=workflow_ids or [],
                agent_ids=agent_ids or [],
                status=status_enum,
            )

            response = self.application_stub.UpdateApplication(request)

            application = response.application
            return {
                "success": response.success,
                "message": response.message,
                "application": {
                    "id": application.id,
                    "user_id": application.user_id,
                    "name": application.name,
                    "description": application.description,
                    "workflow_ids": list(application.workflow_ids),
                    "agent_ids": list(application.agent_ids),
                    "status": application.status,
                    "created_at": application.created_at,
                    "updated_at": application.updated_at,
                },
            }

        except RpcError as e:
            logger.error(f"gRPC error updating application: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}", "application": None}
        except Exception as e:
            logger.error(f"Error updating application: {e}")
            return {"success": False, "message": str(e), "application": None}

    async def delete_application(self, application_id: str, user_id: str) -> Dict[str, Any]:
        """
        Delete an application.

        Args:
            application_id: ID of the application
            user_id: ID of the user

        Returns:
            Dict containing success status and message
        """
        try:
            request = analytics_pb2.DeleteApplicationRequest(
                application_id=application_id, user_id=user_id
            )

            response = self.application_stub.DeleteApplication(request)

            return {"success": response.success, "message": response.message}

        except RpcError as e:
            logger.error(f"gRPC error deleting application: {e}")
            return {"success": False, "message": f"gRPC error: {e.details()}"}
        except Exception as e:
            logger.error(f"Error deleting application: {e}")
            return {"success": False, "message": str(e)}

    async def attach_image_to_application(
        self,
        application_id: str,
        user_id: str,
        image_name: str,
        image_type: str,
        image_data: bytes,
        description: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Attach an image to an application.

        Args:
            application_id: ID of the application
            user_id: ID of the user
            image_name: Name of the image file
            image_type: MIME type of the image (e.g., "image/jpeg", "image/png")
            image_data: Binary image data
            description: Optional description of the image

        Returns:
            Dict containing image attachment details
        """
        try:
            request = analytics_pb2.AttachImageToApplicationRequest(
                application_id=application_id,
                user_id=user_id,
                image_name=image_name,
                image_type=image_type,
                image_data=image_data,
                description=description or "",
            )

            response = self.application_stub.AttachImageToApplication(request)

            return {
                "success": response.success,
                "message": response.message,
                "image_id": response.image_id,
                "image_url": response.image_url,
            }

        except RpcError as e:
            logger.error(f"gRPC error attaching image to application: {e}")
            return {
                "success": False,
                "message": f"gRPC error: {e.details()}",
                "image_id": None,
                "image_url": None,
            }
        except Exception as e:
            logger.error(f"Error attaching image to application: {e}")
            return {"success": False, "message": str(e), "image_id": None, "image_url": None}


# Create a global instance
application_service = ApplicationServiceClient()
