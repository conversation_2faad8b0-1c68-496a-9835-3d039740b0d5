#!/usr/bin/env python3
"""
Test script to investigate the gRPC conversion issue for requirement_rules.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'workflow-service'))

from app.services.workflow_builder.workflow_builder_service import WorkflowBuilderService
from app.services.workflow_builder.component_service import ComponentService
import asyncio

async def test_grpc_conversion():
    """Test the gRPC conversion process for requirement_rules."""
    
    print("=== Testing gRPC Conversion for Requirement Rules ===")
    
    # Create component service and discover components
    component_service = ComponentService()
    components = await component_service.discover_components()
    
    print(f"Discovered {sum(len(cat_comps) for cat_comps in components.values())} components")
    
    # Find the API Request component
    api_request_comp = None
    for category, comps in components.items():
        if "ApiRequestNode" in comps:
            api_request_comp = comps["ApiRequestNode"]
            print(f"Found ApiRequestNode in category: {category}")
            break
    
    if api_request_comp is None:
        print("ERROR: ApiRequestNode not found")
        return
    
    # Check the component definition
    print(f"\nComponent: {api_request_comp.name}")
    print(f"Display Name: {api_request_comp.display_name}")
    print(f"Number of inputs: {len(api_request_comp.inputs)}")
    
    # Find the body input
    body_input = None
    for inp in api_request_comp.inputs:
        if inp.name == 'body':
            body_input = inp
            break
    
    if body_input is None:
        print("ERROR: Body input not found")
        return
    
    print(f"\n=== Body Input from ComponentDefinition ===")
    print(f"Name: {body_input.name}")
    print(f"Display Name: {body_input.display_name}")
    print(f"Required: {body_input.required}")
    print(f"Requirement Rules: {body_input.requirement_rules}")
    print(f"Requirement Logic: {body_input.requirement_logic}")
    
    if body_input.requirement_rules:
        print(f"Number of requirement rules: {len(body_input.requirement_rules)}")
        for i, rule in enumerate(body_input.requirement_rules):
            print(f"  Rule {i}: {rule}")
            print(f"    Type: {type(rule)}")
            if hasattr(rule, 'field_name'):
                print(f"    Field name: {rule.field_name}")
                print(f"    Field value: {rule.field_value}")
                print(f"    Operator: {rule.operator}")
    
    # Now test the gRPC service conversion
    print(f"\n=== Testing gRPC Service Conversion ===")
    
    # Create the gRPC service
    grpc_service = WorkflowBuilderService()
    
    # Create a mock request (empty request for discoverComponents)
    class MockRequest:
        pass
    
    class MockContext:
        pass
    
    # Call the discoverComponents method
    response = grpc_service.discoverComponents(MockRequest(), MockContext())
    
    print(f"gRPC response has {len(response.components)} components")
    
    # Find the API Request component in the response
    api_request_proto = None
    for comp in response.components:
        if comp.name == "ApiRequestNode":
            api_request_proto = comp
            break
    
    if api_request_proto is None:
        print("ERROR: ApiRequestNode not found in gRPC response")
        return
    
    print(f"\n=== API Request Component from gRPC Response ===")
    print(f"Name: {api_request_proto.name}")
    print(f"Display Name: {api_request_proto.display_name}")
    print(f"Number of inputs: {len(api_request_proto.inputs)}")
    
    # Find the body input in the protobuf response
    body_input_proto = None
    for inp in api_request_proto.inputs:
        if inp.name == 'body':
            body_input_proto = inp
            break
    
    if body_input_proto is None:
        print("ERROR: Body input not found in gRPC response")
        return
    
    print(f"\n=== Body Input from gRPC Response ===")
    print(f"Name: {body_input_proto.name}")
    print(f"Display Name: {body_input_proto.display_name}")
    print(f"Required: {body_input_proto.required}")
    print(f"Requirement Rules: {list(body_input_proto.requirement_rules)}")
    print(f"Requirement Logic: {body_input_proto.requirement_logic}")
    
    if body_input_proto.requirement_rules:
        print(f"Number of requirement rules: {len(body_input_proto.requirement_rules)}")
        for i, rule in enumerate(body_input_proto.requirement_rules):
            print(f"  Rule {i}:")
            print(f"    Field name: {rule.field_name}")
            print(f"    Field value: {rule.field_value}")
            print(f"    Operator: {rule.operator}")
    else:
        print("No requirement rules found in gRPC response!")

if __name__ == "__main__":
    asyncio.run(test_grpc_conversion())
