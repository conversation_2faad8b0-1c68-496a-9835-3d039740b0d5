{"name": "Untitled_Workflow", "description": "Untitled_Workflow", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"AlterMetadataComponent-1748512418009_input_metadata": {"node_id": "AlterMetadataComponent-1748512418009", "node_name": "<PERSON>er Metada<PERSON>", "input_name": "input_metadata", "connected_to_start": true, "required": true, "input_type": "dict", "options": null}}}}, "width": 208, "height": 170, "selected": false, "dragging": false}, {"id": "AlterMetadataComponent-1748512418009", "type": "WorkflowNode", "position": {"x": 620, "y": 100}, "data": {"label": "<PERSON>er Metada<PERSON>", "type": "component", "originalType": "AlterMetadataComponent", "definition": {"name": "AlterMetadataComponent", "display_name": "<PERSON>er Metada<PERSON>", "description": "Modifies metadata dictionary keys.", "category": "Processing", "icon": "Tag", "beta": false, "requires_approval": false, "inputs": [{"name": "input_metadata", "display_name": "Input Metadata", "info": "The metadata dictionary to modify. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "updates", "display_name": "<PERSON>ada<PERSON> Updates", "info": "Dictionary of key-value pairs to update or add to the metadata. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "keys_to_remove", "display_name": "Keys to Remove", "info": "List of keys to remove from the metadata. Can be connected from another node or entered directly.", "input_type": "list", "input_types": ["list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "output_metadata", "display_name": "Updated Metadata", "output_type": "dict", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.altermetadatacomponent", "interface_issues": []}, "config": {}}, "style": {"opacity": 1}, "width": 210, "height": 266, "selected": false, "positionAbsolute": {"x": 620, "y": 100}, "dragging": false}, {"id": "CombineTextComponent-1748512427132", "type": "WorkflowNode", "position": {"x": 1040, "y": -400}, "data": {"label": "Combine Text", "type": "component", "originalType": "CombineTextComponent", "definition": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs.", "category": "Processing", "icon": "Link", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 2, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "\\n", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "config": {}}, "style": {"opacity": 1}, "width": 210, "height": 218, "selected": false, "positionAbsolute": {"x": 1040, "y": -400}, "dragging": false}, {"id": "AgenticAI-1748512435550", "type": "WorkflowNode", "position": {"x": 1220, "y": -80}, "data": {"label": "AI Agent Executor", "type": "component", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "input_type": "credential", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "objective", "display_name": "Objective", "info": "The task or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "List of tools available to the agent. Can be connected from another node or entered directly.", "input_type": "list", "input_types": ["list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "agent_type", "display_name": "Agent Type", "info": "The type of agent to create.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "stream", "display_name": "Stream Response", "info": "Enable streaming for real-time responses.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {}}, "style": {"opacity": 1}, "width": 210, "height": 338, "selected": false, "positionAbsolute": {"x": 1220, "y": -80}, "dragging": false}, {"id": "ConditionalNode-1748512442609", "type": "WorkflowNode", "position": {"x": 680, "y": -260}, "data": {"label": "Switch-Case Router", "type": "component", "originalType": "ConditionalNode", "definition": {"name": "ConditionalNode", "display_name": "Switch-Case Router", "description": "Evaluates multiple conditions and routes data to matching outputs", "category": "Logic", "icon": "GitBranch", "beta": false, "requires_approval": false, "inputs": [{"name": "primary_input_data", "display_name": "Primary Input Data", "info": "Main data to route through conditions. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "condition_1_source", "display_name": "Condition 1 - Source", "info": "Source of data for condition evaluation", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "condition_1_variable", "display_name": "Condition 1 - Variable Name", "info": "Global context variable name (only for global_context source)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "condition_1_source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_1_operator", "display_name": "Condition 1 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": true, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "condition_1_expected_value", "display_name": "Condition 1 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "condition_1_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_1_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND"}, {"name": "condition_1_use_primary", "display_name": "Condition 1 - Use Primary Input Data", "info": "Route primary input data when this condition matches", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "condition_1_custom_input", "display_name": "Condition 1 - Custom Input", "info": "Custom data to route when condition matches (only if not using primary input)", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "condition_1_use_primary", "field_value": "False", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_2_source", "display_name": "Condition 2 - Source", "info": "Source of data for condition evaluation", "input_type": "dropdown", "input_types": null, "required": true, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "condition_2_variable", "display_name": "Condition 2 - Variable Name", "info": "Global context variable name (only for global_context source)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "condition_2_source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_2_operator", "display_name": "Condition 2 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": true, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "condition_2_expected_value", "display_name": "Condition 2 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "condition_2_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_2_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND"}, {"name": "condition_2_use_primary", "display_name": "Condition 2 - Use Primary Input Data", "info": "Route primary input data when this condition matches", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "condition_2_custom_input", "display_name": "Condition 2 - Custom Input", "info": "Custom data to route when condition matches (only if not using primary input)", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "condition_2_use_primary", "field_value": "False", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "num_additional_conditions", "display_name": "Number of Additional Conditions", "info": "Number of additional conditions beyond the base 2 conditions (0-8). Total conditions will be 2 + this value.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "condition_3_source", "display_name": "Condition 3 - Source", "info": "Source of data for condition evaluation", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_3_variable", "display_name": "Condition 3 - Variable Name", "info": "Global context variable name (only for global_context source)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_3_source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_3_operator", "display_name": "Condition 3 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_3_expected_value", "display_name": "Condition 3 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_3_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_3_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND"}, {"name": "condition_3_use_primary", "display_name": "Condition 3 - Use Primary Input Data", "info": "Route primary input data when this condition matches", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_3_custom_input", "display_name": "Condition 3 - Custom Input", "info": "Custom data to route when condition matches (only if not using primary input)", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_3_use_primary", "field_value": "False", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_4_source", "display_name": "Condition 4 - Source", "info": "Source of data for condition evaluation", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_4_variable", "display_name": "Condition 4 - Variable Name", "info": "Global context variable name (only for global_context source)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_4_source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_4_operator", "display_name": "Condition 4 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_4_expected_value", "display_name": "Condition 4 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_4_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_4_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND"}, {"name": "condition_4_use_primary", "display_name": "Condition 4 - Use Primary Input Data", "info": "Route primary input data when this condition matches", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_4_custom_input", "display_name": "Condition 4 - Custom Input", "info": "Custom data to route when condition matches (only if not using primary input)", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_4_use_primary", "field_value": "False", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_5_source", "display_name": "Condition 5 - Source", "info": "Source of data for condition evaluation", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_5_variable", "display_name": "Condition 5 - Variable Name", "info": "Global context variable name (only for global_context source)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_5_source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_5_operator", "display_name": "Condition 5 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_5_expected_value", "display_name": "Condition 5 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_5_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_5_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND"}, {"name": "condition_5_use_primary", "display_name": "Condition 5 - Use Primary Input Data", "info": "Route primary input data when this condition matches", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_5_custom_input", "display_name": "Condition 5 - Custom Input", "info": "Custom data to route when condition matches (only if not using primary input)", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_5_use_primary", "field_value": "False", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_6_source", "display_name": "Condition 6 - Source", "info": "Source of data for condition evaluation", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_6_variable", "display_name": "Condition 6 - Variable Name", "info": "Global context variable name (only for global_context source)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_6_source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_6_operator", "display_name": "Condition 6 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_6_expected_value", "display_name": "Condition 6 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_6_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_6_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND"}, {"name": "condition_6_use_primary", "display_name": "Condition 6 - Use Primary Input Data", "info": "Route primary input data when this condition matches", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_6_custom_input", "display_name": "Condition 6 - Custom Input", "info": "Custom data to route when condition matches (only if not using primary input)", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_6_use_primary", "field_value": "False", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_7_source", "display_name": "Condition 7 - Source", "info": "Source of data for condition evaluation", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_7_variable", "display_name": "Condition 7 - Variable Name", "info": "Global context variable name (only for global_context source)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_7_source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_7_operator", "display_name": "Condition 7 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_7_expected_value", "display_name": "Condition 7 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_7_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_7_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND"}, {"name": "condition_7_use_primary", "display_name": "Condition 7 - Use Primary Input Data", "info": "Route primary input data when this condition matches", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_7_custom_input", "display_name": "Condition 7 - Custom Input", "info": "Custom data to route when condition matches (only if not using primary input)", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_7_use_primary", "field_value": "False", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_8_source", "display_name": "Condition 8 - Source", "info": "Source of data for condition evaluation", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_8_variable", "display_name": "Condition 8 - Variable Name", "info": "Global context variable name (only for global_context source)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_8_source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_8_operator", "display_name": "Condition 8 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_8_expected_value", "display_name": "Condition 8 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_8_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_8_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND"}, {"name": "condition_8_use_primary", "display_name": "Condition 8 - Use Primary Input Data", "info": "Route primary input data when this condition matches", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_8_custom_input", "display_name": "Condition 8 - Custom Input", "info": "Custom data to route when condition matches (only if not using primary input)", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_8_use_primary", "field_value": "False", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_9_source", "display_name": "Condition 9 - Source", "info": "Source of data for condition evaluation", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_9_variable", "display_name": "Condition 9 - Variable Name", "info": "Global context variable name (only for global_context source)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_9_source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_9_operator", "display_name": "Condition 9 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_9_expected_value", "display_name": "Condition 9 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_9_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_9_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND"}, {"name": "condition_9_use_primary", "display_name": "Condition 9 - Use Primary Input Data", "info": "Route primary input data when this condition matches", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_9_custom_input", "display_name": "Condition 9 - Custom Input", "info": "Custom data to route when condition matches (only if not using primary input)", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_9_use_primary", "field_value": "False", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_10_source", "display_name": "Condition 10 - Source", "info": "Source of data for condition evaluation", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_10_variable", "display_name": "Condition 10 - Variable Name", "info": "Global context variable name (only for global_context source)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_10_source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_10_operator", "display_name": "Condition 10 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_10_expected_value", "display_name": "Condition 10 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_10_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_10_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND"}, {"name": "condition_10_use_primary", "display_name": "Condition 10 - Use Primary Input Data", "info": "Route primary input data when this condition matches", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}], "visibility_logic": "OR"}, {"name": "condition_10_custom_input", "display_name": "Condition 10 - Custom Input", "info": "Custom data to route when condition matches (only if not using primary input)", "input_type": "multiline", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_10_use_primary", "field_value": "False", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_1_input_handle", "display_name": "Condition 1 Input", "info": "Input data for condition 1 evaluation (only when source is Node Output)", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": [{"field_name": "condition_1_source", "field_value": "node_output", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_2_input_handle", "display_name": "Condition 2 Input", "info": "Input data for condition 2 evaluation (only when source is Node Output)", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": [{"field_name": "condition_2_source", "field_value": "node_output", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_3_input_handle", "display_name": "Condition 3 Input", "info": "Input data for condition 3 evaluation (only when source is Node Output)", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_3_source", "field_value": "node_output", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_4_input_handle", "display_name": "Condition 4 Input", "info": "Input data for condition 4 evaluation (only when source is Node Output)", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_4_source", "field_value": "node_output", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_5_input_handle", "display_name": "Condition 5 Input", "info": "Input data for condition 5 evaluation (only when source is Node Output)", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_5_source", "field_value": "node_output", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_6_input_handle", "display_name": "Condition 6 Input", "info": "Input data for condition 6 evaluation (only when source is Node Output)", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_6_source", "field_value": "node_output", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_7_input_handle", "display_name": "Condition 7 Input", "info": "Input data for condition 7 evaluation (only when source is Node Output)", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_7_source", "field_value": "node_output", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_8_input_handle", "display_name": "Condition 8 Input", "info": "Input data for condition 8 evaluation (only when source is Node Output)", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_8_source", "field_value": "node_output", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_9_input_handle", "display_name": "Condition 9 Input", "info": "Input data for condition 9 evaluation (only when source is Node Output)", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_9_source", "field_value": "node_output", "operator": "equals"}], "visibility_logic": "AND"}, {"name": "condition_10_input_handle", "display_name": "Condition 10 Input", "info": "Input data for condition 10 evaluation (only when source is Node Output)", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "equals"}, {"field_name": "condition_10_source", "field_value": "node_output", "operator": "equals"}], "visibility_logic": "AND"}], "outputs": [{"name": "default_output", "display_name": "<PERSON><PERSON><PERSON>", "output_type": "Any", "method": null}], "is_valid": true, "path": "components.logic.conditionalnode", "interface_issues": []}, "config": {"condition_1_operator": "ends_with", "condition_1_expected_value": "hello", "condition_2_source": "global_context", "condition_2_variable": "marketing", "condition_2_operator": "exists", "num_additional_conditions": "1", "condition_3_operator": "ends_with"}}, "style": {"opacity": 1}, "width": 208, "height": 337, "selected": false, "positionAbsolute": {"x": 680, "y": -260}, "dragging": false}, {"id": "MessageToDataComponent-1748512570114", "type": "WorkflowNode", "position": {"x": 1340, "y": 280}, "data": {"label": "Message To Data", "type": "component", "originalType": "MessageToDataComponent", "definition": {"name": "MessageToDataComponent", "display_name": "Message To Data", "description": "Extracts fields from a Message object.", "category": "Processing", "icon": "Package", "beta": false, "requires_approval": false, "inputs": [{"name": "input_message", "display_name": "Input Message", "info": "The Message object to extract fields from. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["Message", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "fields_to_extract", "display_name": "Fields to Extract", "info": "List of field names to extract from the message. Leave empty to extract all fields. Can be connected from another node or entered directly.", "input_type": "list", "input_types": ["list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "output_data", "display_name": "Extracted Data", "output_type": "dict", "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "method": null}], "is_valid": true, "path": "components.processing.messagetodatacomponent", "interface_issues": []}, "config": {}}, "style": {"opacity": 1}, "width": 210, "height": 242, "selected": false, "positionAbsolute": {"x": 1340, "y": 280}, "dragging": false}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "start-node", "sourceHandle": "flow", "target": "AlterMetadataComponent-1748512418009", "targetHandle": "input_metadata", "type": "default", "id": "reactflow__edge-start-nodeflow-AlterMetadataComponent-1748512418009input_metadata", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "AlterMetadataComponent-1748512418009", "sourceHandle": "output_metadata", "target": "ConditionalNode-1748512442609", "targetHandle": "primary_input_data", "type": "default", "id": "reactflow__edge-AlterMetadataComponent-1748512418009output_metadata-ConditionalNode-1748512442609primary_input_data", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "ConditionalNode-1748512442609", "sourceHandle": "condition_1_output", "target": "CombineTextComponent-1748512427132", "targetHandle": "main_input", "type": "default", "id": "reactflow__edge-ConditionalNode-1748512442609condition_1_output-CombineTextComponent-1748512427132main_input", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "ConditionalNode-1748512442609", "sourceHandle": "condition_2_output", "target": "AgenticAI-1748512435550", "targetHandle": "objective", "type": "default", "id": "reactflow__edge-ConditionalNode-1748512442609condition_2_output-AgenticAI-1748512435550objective", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "ConditionalNode-1748512442609", "sourceHandle": "condition_3_output", "target": "MessageToDataComponent-1748512570114", "targetHandle": "input_message", "type": "default", "id": "reactflow__edge-ConditionalNode-1748512442609condition_3_output-MessageToDataComponent-1748512570114input_message", "selected": false}]}, "start_node_data": [{"field": "input_metadata", "type": "dict", "transition_id": "AlterMetadataComponent-1748512418009"}]}