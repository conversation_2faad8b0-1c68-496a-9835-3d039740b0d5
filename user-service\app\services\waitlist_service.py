from typing import Any, Dict
import grpc
from passlib.context import CryptContext
from app.models.user import User
from app.grpc import user_pb2, user_pb2_grpc
from app.db.session import SessionLocal
from app.models.waitlist import WaitlistEntry, WaitlistStatus
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.redis.redis_service import RedisService
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class WaitlistService(user_pb2_grpc.UserServiceServicer):
    def __init__(self):
        self.redis_service = RedisService()
        self.kafka_producer = KafkaProducer()
    
    def get_db(self):
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    
    def send_waitlist_confirmation_email(
            self, email: str, email_type: str
        ) -> Dict[str, str]:
            try:
                action = "addToWaitlist" if email_type == SendEmailTypeEnum.ADD_TO_WAITLIST.value else "markedAsApproved"
                title = "Welcome to Waitlist" if action == "addToWaitlist" else "Your Account is Approved"
                body = "You've been added to our waitlist" if action == "addToWaitlist" else "Your account has been approved"
                
                self.kafka_producer.send_email_event_unified(
                    email_type=email_type,
                    data={
                        "emailId": email,
                        "title": title,
                        "link": "https://example.com/waitlist-status",
                        "logo": "https://example.com/logo.png",
                        "body": body
                    },
                    action=action
                )
            except Exception as e:
                raise Exception("Failed to send email")
    
    # --- New AddToWaitlist Method ---
    def addToWaitlist(self, request: user_pb2.AddToWaitlistRequest, context: grpc.ServicerContext) -> user_pb2.AddToWaitlistResponse:
        db = self.get_db()
        try:
            # Perform checks
            print(f"Adding {request.email} to the waitlist...")

            # 1. Check if email is already registered as a full user
            existing_user = db.query(User).filter(User.email == request.email).first()
            if existing_user and existing_user.is_active and existing_user.is_email_verified:
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("This email is already registered.")
                return user_pb2.AddToWaitlistResponse(success=False, message="This email is already registered.")

            # 2. Check if email is already on the waitlist
            existing_waitlist_entry = db.query(WaitlistEntry).filter(WaitlistEntry.email == request.email).first()
            print(f"Existing waitlist entry: {existing_waitlist_entry}")
            if existing_waitlist_entry:
                # Optionally update details if they resubmit? Or just inform.
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("This email is already on the waitlist.")
                return user_pb2.AddToWaitlistResponse(success=False, message="This email is already on the waitlist.")

            # 3. Create new waitlist entry
            new_entry = WaitlistEntry(
                email=request.email
            )

            print(f"New waitlist entry: {new_entry}")

            db.add(new_entry)
            db.commit()

            print(f"Added {request.email} to the waitlist.")

            # Optional: Send a confirmation email to the users
            self.send_waitlist_confirmation_email(request.email, SendEmailTypeEnum.ADD_TO_WAITLIST.value)

            print(f"Confirmation email sent to {request.email}.")

            return user_pb2.AddToWaitlistResponse(success=True, message="You've been added to the waitlist!")

        except Exception as e:
            db.rollback()
            print(f"Error adding to waitlist: {e}") # Log the error
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An internal error occurred while adding to the waitlist.")
            return user_pb2.AddToWaitlistResponse(success=False, message="Failed to join waitlist.")
        finally:
            db.close()

    # --- Get Waitlist Method ---
    def getWaitlist(self, request: user_pb2.GetWaitlistRequest, context: grpc.ServicerContext) -> user_pb2.GetWaitlistResponse:
        db = self.get_db()
        try:
            # Parse pagination parameters with defaults
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            
            # Create base query
            query = db.query(WaitlistEntry)
            
            # Apply status filter if provided
            if request.status_filter:
                query = query.filter(WaitlistEntry.status == request.status_filter)
            
            # Get total count before pagination
            total_count = query.count()
            
            # Calculate total pages
            total_pages = (total_count + page_size - 1) // page_size
            
            # Apply pagination
            offset = (page - 1) * page_size
            waitlist_entries = query.order_by(WaitlistEntry.joined_at.desc()).offset(offset).limit(page_size).all()
            
            # Convert entries to proto format
            entries_pb = []
            for entry in waitlist_entries:
                entry_pb = user_pb2.WaitlistEntry(
                    id=entry.id,
                    email=entry.email,
                    joined_at=entry.joined_at.isoformat(),
                    status=entry.status.value
                )
                entries_pb.append(entry_pb)
            
            return user_pb2.GetWaitlistResponse(
                entries=entries_pb,
                total=total_count,
                page=page,
                total_pages=total_pages
            )
            
        except Exception as e:
            db.rollback()
            print(f"Error getting waitlist: {e}")  # Log the error
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"An internal error occurred while getting the waitlist: {str(e)}")
            return user_pb2.GetWaitlistResponse()
        finally:
            db.close()

    # --- Approve Waitlist User Method ---
    def approveWaitlistUser(self, request: user_pb2.ApproveWaitlistUserRequest, context: grpc.ServicerContext) -> user_pb2.ApproveWaitlistUserResponse:
        db = self.get_db()
        try:
            print(f"Approving user with id {request.id}...")
            # Find the waitlist entry
            entry = db.query(WaitlistEntry).filter(WaitlistEntry.id == request.id).first()
            print(f"Found entry: {entry}")
            if not entry:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Waitlist entry with id {request.id} not found.")
                return user_pb2.ApproveWaitlistUserResponse(success=False, message=f"Waitlist entry not found.")

            # Update status to approved
            entry.status = WaitlistStatus.APPROVED.value
            print(f"Updated status to approved for entry: {entry.status}")
            db.commit()
            
            # Optional: Send approval notification email
            self.send_waitlist_confirmation_email(entry.email, SendEmailTypeEnum.USER_APPROVED.value)
            
            return user_pb2.ApproveWaitlistUserResponse(success=True, message=f"User {entry.email} has been approved.")
            
        except Exception as e:
            db.rollback()
            print(f"Error approving waitlist user: {e}")  # Log the error
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"An internal error occurred: {str(e)}")
            return user_pb2.ApproveWaitlistUserResponse(success=False, message="Failed to approve user.")
        finally:
            db.close()

    # --- Approve Multiple Waitlist Users Method ---
    def approveMultipleWaitlistUsers(self, request: user_pb2.ApproveMultipleWaitlistUsersRequest, context: grpc.ServicerContext) -> user_pb2.ApproveMultipleWaitlistUsersResponse:
        db = self.get_db()
        try:
            if not request.ids:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("No user IDs provided.")
                return user_pb2.ApproveMultipleWaitlistUsersResponse(
                    success=False, 
                    message="No user IDs provided.",
                    approved_count=0,
                    failed_ids=[]
                )
                
            # Keep track of successfully approved and failed IDs
            approved_count = 0
            failed_ids = []
            
            for user_id in request.ids:
                entry = db.query(WaitlistEntry).filter(WaitlistEntry.id == user_id).first()
                
                if not entry:
                    failed_ids.append(user_id)
                    continue
                    
                # Update status to approved
                entry.status = WaitlistStatus.APPROVED.value
                approved_count += 1
                
                # Send approval notification email

                self.send_waitlist_confirmation_email(entry.email, SendEmailTypeEnum.USER_APPROVED.value)
            
            db.commit()
            
            # Determine success based on whether all users were approved
            success = approved_count > 0 and len(failed_ids) == 0
            message = (
                f"All {approved_count} users approved successfully." if success
                else f"{approved_count} users approved. {len(failed_ids)} users failed."
            )
            
            return user_pb2.ApproveMultipleWaitlistUsersResponse(
                success=success,
                message=message,
                approved_count=approved_count,
                failed_ids=failed_ids
            )
            
        except Exception as e:
            db.rollback()
            print(f"Error approving multiple waitlist users: {e}")  # Log the error
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"An internal error occurred: {str(e)}")
            return user_pb2.ApproveMultipleWaitlistUsersResponse(
                success=False, 
                message=f"Error: {str(e)}",
                approved_count=0,
                failed_ids=request.ids
            )
        finally:
            db.close()
