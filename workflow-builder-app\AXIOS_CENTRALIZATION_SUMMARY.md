# Axios Interceptor Centralization Summary

## Overview
Successfully centralized all axios interceptors into a single, maintainable system located in `src/utils/axios.ts`.

## What Was Done

### 1. Enhanced Main Axios Configuration (`src/utils/axios.ts`)
- Created a centralized axios instance factory with configurable options
- Added support for different authentication modes (client-side vs server-side tokens)
- Implemented comprehensive token refresh logic
- Created specialized instances for different use cases:
  - `api` - Main API instance with full token refresh capabilities
  - `workflowApi` - Workflow-specific instance with client-side token support
  - `authApi` - Authentication instance with credentials support
  - `externalApi` - External API instance without authentication

### 2. Configuration Interface
```typescript
interface AxiosInstanceConfig {
  baseURL?: string;
  withCredentials?: boolean;
  enableTokenRefresh?: boolean;
  enableClientSideToken?: boolean;
  customHeaders?: Record<string, string>;
}
```

### 3. Files Updated

#### Replaced Individual Axios Instances:
- ✅ `src/app/(features)/workflows/api.ts` - Replaced `workflowAxios` with `workflowApi`
- ✅ `src/app/(features)/auth/api.ts` - Replaced `authAxios` with `centralizedAuthApi`
- ✅ `src/lib/authApi.ts` - Replaced `authAxios` with `centralizedAuthApi`
- ✅ `src/lib/authenticatedApi.ts` - Replaced `authenticatedAxios` with `api`
- ✅ `src/lib/workflowApi.ts` - Replaced direct axios with `externalApi`
- ✅ `src/app/api/auth.ts` - Replaced direct axios with `centralizedAuthApi`

#### Direct Axios Calls Replaced:
- All `axios.get()`, `axios.post()`, `axios.delete()`, `axios.patch()` calls
- External URL fetching now uses `externalApi` (no auth headers)
- Internal API calls use appropriate centralized instances

### 4. Benefits Achieved

#### Consistency
- All API calls now use the same interceptor logic
- Consistent error handling across the application
- Unified token management

#### Maintainability
- Single source of truth for axios configuration
- Easy to modify interceptor behavior globally
- Reduced code duplication

#### Flexibility
- Different instances for different use cases
- Configurable authentication modes
- Support for both client-side and server-side environments

#### Security
- Centralized token refresh logic
- Consistent authorization header handling
- Proper error handling for authentication failures

### 5. Instance Usage Guide

```typescript
// For internal API calls with authentication
import api from "@/utils/axios";
const response = await api.get("/api/endpoint");

// For workflow-specific calls (client-side token support)
import { workflowApi } from "@/utils/axios";
const response = await workflowApi.get("/workflows");

// For authentication calls
import { authApi } from "@/utils/axios";
const response = await authApi.post("/auth/login", data);

// For external URLs (no authentication)
import { externalApi } from "@/utils/axios";
const response = await externalApi.get("https://external-api.com/data");
```

### 6. Key Features

#### Automatic Token Management
- Automatically adds Bearer tokens to requests
- Supports both client-side and server-side token retrieval
- Handles token refresh on 401/403 errors

#### Environment Awareness
- Detects client vs server environment
- Uses appropriate token retrieval methods
- Handles window object availability

#### Error Handling
- Comprehensive error handling for network issues
- Automatic logout on authentication failures
- User store cleanup on token expiration

## Migration Complete
All axios interceptors have been successfully centralized. The application now uses a single, maintainable axios configuration system that provides consistent behavior across all API calls.
