# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.grpc_ import google_drive_pb2 as google__drive__pb2

GRPC_GENERATED_VERSION = '1.70.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in google_drive_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class GoogleDriveServiceStub(object):
    """Google Drive integration service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.getAuthUrl = channel.unary_unary(
                '/google_drive.GoogleDriveService/getAuthUrl',
                request_serializer=google__drive__pb2.GetAuthUrlRequest.SerializeToString,
                response_deserializer=google__drive__pb2.GetAuthUrlResponse.FromString,
                _registered_method=True)
        self.connectDrive = channel.unary_unary(
                '/google_drive.GoogleDriveService/connectDrive',
                request_serializer=google__drive__pb2.ConnectDriveRequest.SerializeToString,
                response_deserializer=google__drive__pb2.ConnectDriveResponse.FromString,
                _registered_method=True)
        self.disconnectDrive = channel.unary_unary(
                '/google_drive.GoogleDriveService/disconnectDrive',
                request_serializer=google__drive__pb2.DisconnectDriveRequest.SerializeToString,
                response_deserializer=google__drive__pb2.DisconnectDriveResponse.FromString,
                _registered_method=True)
        self.syncDrive = channel.unary_unary(
                '/google_drive.GoogleDriveService/syncDrive',
                request_serializer=google__drive__pb2.SyncDriveRequest.SerializeToString,
                response_deserializer=google__drive__pb2.SyncDriveResponse.FromString,
                _registered_method=True)
        self.listFiles = channel.unary_unary(
                '/google_drive.GoogleDriveService/listFiles',
                request_serializer=google__drive__pb2.ListFilesRequest.SerializeToString,
                response_deserializer=google__drive__pb2.ListFilesResponse.FromString,
                _registered_method=True)
        self.getFileDetails = channel.unary_unary(
                '/google_drive.GoogleDriveService/getFileDetails',
                request_serializer=google__drive__pb2.GetFileDetailsRequest.SerializeToString,
                response_deserializer=google__drive__pb2.GetFileDetailsResponse.FromString,
                _registered_method=True)
        self.getFolderById = channel.unary_unary(
                '/google_drive.GoogleDriveService/getFolderById',
                request_serializer=google__drive__pb2.GetFolderByIdRequest.SerializeToString,
                response_deserializer=google__drive__pb2.GetFolderByIdResponse.FromString,
                _registered_method=True)
        self.syncFolderByIds = channel.unary_unary(
                '/google_drive.GoogleDriveService/syncFolderByIds',
                request_serializer=google__drive__pb2.SyncFolderByIdsRequest.SerializeToString,
                response_deserializer=google__drive__pb2.SyncFolderByIdsResponse.FromString,
                _registered_method=True)
        self.checkFileAccess = channel.unary_unary(
                '/google_drive.GoogleDriveService/checkFileAccess',
                request_serializer=google__drive__pb2.CheckFileAccessRequest.SerializeToString,
                response_deserializer=google__drive__pb2.CheckFileAccessResponse.FromString,
                _registered_method=True)
        self.searchSimilarDocuments = channel.unary_unary(
                '/google_drive.GoogleDriveService/searchSimilarDocuments',
                request_serializer=google__drive__pb2.SearchSimilarDocumentsRequest.SerializeToString,
                response_deserializer=google__drive__pb2.SearchSimilarDocumentsResponse.FromString,
                _registered_method=True)
        self.batchSearchSimilarDocuments = channel.unary_unary(
                '/google_drive.GoogleDriveService/batchSearchSimilarDocuments',
                request_serializer=google__drive__pb2.BatchSearchSimilarDocumentsRequest.SerializeToString,
                response_deserializer=google__drive__pb2.BatchSearchSimilarDocumentsResponse.FromString,
                _registered_method=True)


class GoogleDriveServiceServicer(object):
    """Google Drive integration service
    """

    def getAuthUrl(self, request, context):
        """Get OAuth URL for Google Drive authorization
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def connectDrive(self, request, context):
        """Connect Google Drive account
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def disconnectDrive(self, request, context):
        """Disconnect Google Drive account
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def syncDrive(self, request, context):
        """Sync Google Drive files and folders
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def listFiles(self, request, context):
        """List Google Drive files and folders
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getFileDetails(self, request, context):
        """Get file details
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def getFolderById(self, request, context):
        """Get folder by ID and its contents
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def syncFolderByIds(self, request, context):
        """Sync folders by IDs
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def checkFileAccess(self, request, context):
        """Check if a user has access to a file
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def searchSimilarDocuments(self, request, context):
        """Search for documents semantically similar to a query
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def batchSearchSimilarDocuments(self, request, context):
        """Batch search for documents semantically similar to multiple queries
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GoogleDriveServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'getAuthUrl': grpc.unary_unary_rpc_method_handler(
                    servicer.getAuthUrl,
                    request_deserializer=google__drive__pb2.GetAuthUrlRequest.FromString,
                    response_serializer=google__drive__pb2.GetAuthUrlResponse.SerializeToString,
            ),
            'connectDrive': grpc.unary_unary_rpc_method_handler(
                    servicer.connectDrive,
                    request_deserializer=google__drive__pb2.ConnectDriveRequest.FromString,
                    response_serializer=google__drive__pb2.ConnectDriveResponse.SerializeToString,
            ),
            'disconnectDrive': grpc.unary_unary_rpc_method_handler(
                    servicer.disconnectDrive,
                    request_deserializer=google__drive__pb2.DisconnectDriveRequest.FromString,
                    response_serializer=google__drive__pb2.DisconnectDriveResponse.SerializeToString,
            ),
            'syncDrive': grpc.unary_unary_rpc_method_handler(
                    servicer.syncDrive,
                    request_deserializer=google__drive__pb2.SyncDriveRequest.FromString,
                    response_serializer=google__drive__pb2.SyncDriveResponse.SerializeToString,
            ),
            'listFiles': grpc.unary_unary_rpc_method_handler(
                    servicer.listFiles,
                    request_deserializer=google__drive__pb2.ListFilesRequest.FromString,
                    response_serializer=google__drive__pb2.ListFilesResponse.SerializeToString,
            ),
            'getFileDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.getFileDetails,
                    request_deserializer=google__drive__pb2.GetFileDetailsRequest.FromString,
                    response_serializer=google__drive__pb2.GetFileDetailsResponse.SerializeToString,
            ),
            'getFolderById': grpc.unary_unary_rpc_method_handler(
                    servicer.getFolderById,
                    request_deserializer=google__drive__pb2.GetFolderByIdRequest.FromString,
                    response_serializer=google__drive__pb2.GetFolderByIdResponse.SerializeToString,
            ),
            'syncFolderByIds': grpc.unary_unary_rpc_method_handler(
                    servicer.syncFolderByIds,
                    request_deserializer=google__drive__pb2.SyncFolderByIdsRequest.FromString,
                    response_serializer=google__drive__pb2.SyncFolderByIdsResponse.SerializeToString,
            ),
            'checkFileAccess': grpc.unary_unary_rpc_method_handler(
                    servicer.checkFileAccess,
                    request_deserializer=google__drive__pb2.CheckFileAccessRequest.FromString,
                    response_serializer=google__drive__pb2.CheckFileAccessResponse.SerializeToString,
            ),
            'searchSimilarDocuments': grpc.unary_unary_rpc_method_handler(
                    servicer.searchSimilarDocuments,
                    request_deserializer=google__drive__pb2.SearchSimilarDocumentsRequest.FromString,
                    response_serializer=google__drive__pb2.SearchSimilarDocumentsResponse.SerializeToString,
            ),
            'batchSearchSimilarDocuments': grpc.unary_unary_rpc_method_handler(
                    servicer.batchSearchSimilarDocuments,
                    request_deserializer=google__drive__pb2.BatchSearchSimilarDocumentsRequest.FromString,
                    response_serializer=google__drive__pb2.BatchSearchSimilarDocumentsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'google_drive.GoogleDriveService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('google_drive.GoogleDriveService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class GoogleDriveService(object):
    """Google Drive integration service
    """

    @staticmethod
    def getAuthUrl(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/getAuthUrl',
            google__drive__pb2.GetAuthUrlRequest.SerializeToString,
            google__drive__pb2.GetAuthUrlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def connectDrive(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/connectDrive',
            google__drive__pb2.ConnectDriveRequest.SerializeToString,
            google__drive__pb2.ConnectDriveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def disconnectDrive(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/disconnectDrive',
            google__drive__pb2.DisconnectDriveRequest.SerializeToString,
            google__drive__pb2.DisconnectDriveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def syncDrive(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/syncDrive',
            google__drive__pb2.SyncDriveRequest.SerializeToString,
            google__drive__pb2.SyncDriveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def listFiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/listFiles',
            google__drive__pb2.ListFilesRequest.SerializeToString,
            google__drive__pb2.ListFilesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getFileDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/getFileDetails',
            google__drive__pb2.GetFileDetailsRequest.SerializeToString,
            google__drive__pb2.GetFileDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def getFolderById(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/getFolderById',
            google__drive__pb2.GetFolderByIdRequest.SerializeToString,
            google__drive__pb2.GetFolderByIdResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def syncFolderByIds(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/syncFolderByIds',
            google__drive__pb2.SyncFolderByIdsRequest.SerializeToString,
            google__drive__pb2.SyncFolderByIdsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def checkFileAccess(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/checkFileAccess',
            google__drive__pb2.CheckFileAccessRequest.SerializeToString,
            google__drive__pb2.CheckFileAccessResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def searchSimilarDocuments(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/searchSimilarDocuments',
            google__drive__pb2.SearchSimilarDocumentsRequest.SerializeToString,
            google__drive__pb2.SearchSimilarDocumentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def batchSearchSimilarDocuments(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/google_drive.GoogleDriveService/batchSearchSimilarDocuments',
            google__drive__pb2.BatchSearchSimilarDocumentsRequest.SerializeToString,
            google__drive__pb2.BatchSearchSimilarDocumentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
