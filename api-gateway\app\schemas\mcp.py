from pydantic import BaseModel, <PERSON>, validator
from typing import Dict, Any, Optional, List
import json
from enum import Enum


class McpDepartment(str, Enum):
    GENERAL = "general"
    SALES = "sales"
    MARKETING = "marketing"
    ENGINEERING = "engineering"
    FINANCE = "finance"
    HR = "hr"


class UrlTypeEnum(str, Enum):
    SSE = "sse"
    HTTP = "http"


class VisibilityEnum(str, Enum):
    PRIVATE = "private"
    PUBLIC = "public"


class StatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"


class Owner(BaseModel):
    id: str
    name: Optional[str] = None
    email: Optional[str] = None


class MCPUrl(BaseModel):
    url: str
    type: UrlTypeEnum


class MCPCreate(BaseModel):
    logo: Optional[str] = None
    name: str
    description: Optional[str] = None
    git_url: Optional[str] = None
    git_branch: Optional[str] = None
    urls: Optional[List[MCPUrl]] = None
    github_access_token: Optional[str] = None
    department: McpDepartment
    visibility: VisibilityEnum
    tags: Optional[List[str]] = None
    status: StatusEnum
    user_ids: Optional[List[str]] = None


class CreateMCPResponse(BaseModel):
    success: bool
    message: str
    mcp_id: Optional[str] = None


class OwnerTypeEnum(str, Enum):
    USER = "user"
    ORGANIZATION = "organization"
    PLATFORM = "platform"


class MCPPatchPayload(BaseModel):
    name: Optional[str] = Field(None, min_length=1)
    logo: Optional[str] = None
    description: Optional[str] = None
    visibility: Optional[VisibilityEnum] = None
    user_ids: Optional[List[str]] = None
    department: Optional[McpDepartment] = None
    tags: Optional[List[str]] = None
    status: Optional[StatusEnum] = None
    urls: Optional[List[MCPUrl]] = None
    github_access_token: Optional[str] = None
    git_url: Optional[str] = None
    git_branch: Optional[str] = None

    class Config:
        use_enum_values = True


class DeploymentStatus(str, Enum):
    PENDING = "pending"
    COMPLETED = "completed"


class MCPInDB(BaseModel):
    id: str
    name: str
    logo: Optional[str] = None
    description: str
    owner_id: str
    user_ids: Optional[List[str]] = None
    owner_type: str
    urls: Optional[List[MCPUrl]] = None
    git_url: Optional[str] = None
    git_branch: Optional[str] = None
    deployment_status: Optional[DeploymentStatus] = None
    visibility: str
    tags: Optional[List[str]] = None
    status: str
    created_at: str
    updated_at: str
    department: Optional[str] = None
    mcp_tools_config: Optional[dict] = None
    is_added: Optional[bool] = False

    @validator("mcp_tools_config", pre=True)
    def validate_mcp_tools_config(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v

    @validator("urls", pre=True)
    def validate_urls(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return None
        return v

    class Config:
        orm_mode = True


class MCPResponse(BaseModel):
    success: bool
    message: str
    mcp: Optional[MCPInDB] = None


class CreateMCPResponse(BaseModel):
    success: bool
    message: str
    mcp_id: Optional[str] = None


class UpdateMCPResponse(BaseModel):
    success: bool
    message: str
    mcp: Optional[MCPInDB] = None


class DeleteMCPResponse(BaseModel):
    success: bool
    message: str


class PaginationMetadata(BaseModel):
    total: int
    totalPages: int
    currentPage: int
    pageSize: int
    hasNextPage: bool
    hasPreviousPage: bool


class PaginatedMCPResponse(BaseModel):
    data: List[MCPInDB]
    metadata: PaginationMetadata


class CreateMcpFromTemplateRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    owner_type: OwnerTypeEnum

    class Config:
        use_enum_values = True


class CreateMcpFromTemplateResponse(BaseModel):
    success: bool
    message: str
    mcp_id: Optional[str] = None


class MCPsByIdsRequest(BaseModel):
    ids: List[str]


class MCPsByIdsResponse(BaseModel):
    success: bool
    message: str
    mcps: List[MCPInDB]
    total: int


class MCPUrlsRequest(BaseModel):
    ids: List[str]


class MCPUrlItem(BaseModel):
    mcp_id: str
    url: Optional[str] = None


class MCPUrlsResponse(BaseModel):
    success: bool
    message: str
    urls: List[MCPUrlItem]


class DeploymentUpdatePayload(BaseModel):
    mcp_id: str
    deployment_status: DeploymentStatus
    urls: Optional[List[MCPUrl]] = None
    
    class Config:
        use_enum_values = True


class DeploymentUpdateResponse(BaseModel):
    success: bool
    message: str
    mcp: Optional[MCPInDB] = None
