from fastapi import APIRouter, Depends, HTTPException, Query
from app.services.user_service import UserServiceClient
from app.services.workflow_service import WorkflowServiceClient
import json
from app.utils.parse_error import parse_error
from app.schemas.workflow import (
    PaginatedWorkflowResponse,
    PaginationMetadata,
    ToggleVisibilityResponseAPI,
    UpdateWorkflowResponse,
    WorkflowCategoryEnum,
    WorkflowPatchPayload,
    WorkflowResponse,
    WorkflowCreate,
    WorkflowInDB,
    CreateWorkflowResponse,
    DeleteWorkflowResponse,
    WorkflowStatusEnum,
    WorkflowVisibilityEnum,
    WorkflowsByIdsRequest as WorkflowsByIdsRequestPayload,
    WorkflowsByIdsApiResponse,
)
from google.protobuf.json_format import MessageToDict
from app.core.auth_guard import role_required
from app.core.security import validate_server_auth_key
from typing import Optional, Dict, Any


workflow_router = APIRouter(prefix="/workflows", tags=["workflows"])

workflow_service = WorkflowServiceClient()
user_service = UserServiceClient()


# Helper functions to reduce code duplication
def parse_json_tags(workflow_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Parse JSON string tags into Python dictionaries."""
    if "tags" in workflow_dict and isinstance(workflow_dict["tags"], str):
        try:
            workflow_dict["tags"] = json.loads(workflow_dict["tags"])
        except json.JSONDecodeError:
            workflow_dict["tags"] = None
    return workflow_dict


async def validate_user(user_id: str) -> Dict[str, Any]:
    """Validate a user and return user details."""
    validate_response = await user_service.validate_user(user_id)
    if not validate_response["success"]:
        raise HTTPException(status_code=400, detail=validate_response["message"])
    return validate_response["user"]


def handle_grpc_response_status(response, success_code=200):
    """Handle common gRPC response status code mapping."""
    if not response.success:
        http_status_code = 400
        if "not found" in response.message.lower():
            http_status_code = 404
        elif "permission denied" in response.message.lower():
            http_status_code = 403
        elif "invalid argument" in response.message.lower():
            http_status_code = 400
        else:
            http_status_code = 500
        raise HTTPException(status_code=http_status_code, detail=response.message)
    return success_code


def prepare_workflow_dict(raw_dict):
    # Decode nested fields
    if "tags" in raw_dict and isinstance(raw_dict["tags"], str):
        try:
            raw_dict["tags"] = json.loads(raw_dict["tags"])
        except json.JSONDecodeError:
            raw_dict["tags"] = None

    if "start_nodes" in raw_dict:
        raw_dict["start_nodes"] = [
            json.loads(n) if isinstance(n, str) else n for n in raw_dict["start_nodes"]
        ]
    if "available_nodes" in raw_dict:
        raw_dict["available_nodes"] = [
            json.loads(n) if isinstance(n, str) else n for n in raw_dict["available_nodes"]
        ]

    return raw_dict


@workflow_router.post("", response_model=CreateWorkflowResponse)
async def create_workflow(
    workflow_data: WorkflowCreate, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Create a new workflow.

    This endpoint creates a new workflow with the provided data. The workflow is associated
    with the authenticated user who makes the request.

    ## Request Body
    - **name**: The name of the workflow
    - **workflow_data**: The workflow definition data
    - **start_node_data**: List of start node IDs

    ## Response
    Returns a success message and the ID of the created workflow.

    ## Errors
    - 400: Bad request (invalid input, validation failed)
    - 500: Server error
    """
    try:
        # Validate user and get user details
        user_id = current_user.get("user_id")
        role = current_user.get("role")

        # user_id = "fce79072-a235-4127-ac5b-b5b1709a8077"
        # role = "user"

        response = await workflow_service.create_workflow(
            name=workflow_data.name,
            workflow_data=workflow_data.workflow_data,
            owner_details={"id": user_id},
            start_nodes=workflow_data.start_node_data,
            owner_type=role,
        )

        handle_grpc_response_status(response)

        workflow_dict = MessageToDict(response.workflow, preserving_proto_field_name=True)
        workflow_dict = parse_json_tags(workflow_dict)

        workflow_dict = prepare_workflow_dict(workflow_dict)

        print(f"[DEBUG] Available nodes converted to dict: {workflow_dict.get('available_nodes', [])}")
        return CreateWorkflowResponse(
            success=response.success, message=response.message, workflow_id=workflow_dict["id"]
        )

    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in create_workflow: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.get("/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(
    workflow_id: str, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Retrieve a workflow by its ID.

    This endpoint fetches a workflow from the workflow service and returns its details.
    The workflow data is returned with any JSON string fields parsed into proper objects.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow to retrieve

    ## Response
    Returns the workflow details if found.

    ## Errors
    - 404: Workflow not found
    - 500: Server error
    """
    try:
        user_id = None if current_user.get("role") == "admin" else current_user.get("user_id")

        response = await workflow_service.get_workflow(
            workflow_id=workflow_id,
            user_id=user_id,
        )

        handle_grpc_response_status(response)

        workflow_dict = prepare_workflow_dict(
            MessageToDict(response.workflow, preserving_proto_field_name=True)
        )

        workflow_dict = parse_json_tags(workflow_dict)

        return WorkflowResponse(
            success=response.success, message=response.message, workflow=workflow_dict
        )

    except Exception as e:
        print(f"[EXCEPTION] {e}")
        raise HTTPException(status_code=500, detail=str(e))


@workflow_router.get(
    "/orchestration/{workflow_id}",
    dependencies=[Depends(validate_server_auth_key)],
)
async def get_workflow_orchestration(workflow_id: str):
    """
    Retrieve workflow details for orchestration.

    This endpoint fetches workflow details based on the provided `workflow_id`.
    It ensures that only authorized services with a valid server authentication key can access the workflow data.

    :param workflow_id: The unique identifier of the workflow.
    :type workflow_id: int
    :return: A response containing workflow details if found.
    :rtype: WorkflowResponse
    :raises HTTPException 404: If the workflow is not found.
    :raises HTTPException 500: If an internal server error occurs.
    """
    try:
        print(f"Workflow Id: {workflow_id}")

        response = await workflow_service.get_workflow(workflow_id=workflow_id)

        print(f"response: {response}")

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        workflow_dict = prepare_workflow_dict(
            MessageToDict(response.workflow, preserving_proto_field_name=True)
        )

        print(f"Workflow_Dict: {workflow_dict}")

        # Parse `tags fields if they are JSON strings
        if "tags" in workflow_dict and isinstance(workflow_dict["tags"], str):
            try:
                workflow_dict["tags"] = json.loads(workflow_dict["tags"])
            except json.JSONDecodeError:
                workflow_dict["tags"] = None

        return WorkflowResponse(
            success=response.success, message=response.message, workflow=workflow_dict
        )

    except Exception as e:
        print(f"[EXCEPTION] {e}")
        raise HTTPException(status_code=500, detail=str(e))


@workflow_router.delete("/{workflow_id}", response_model=DeleteWorkflowResponse)
async def delete_workflow(
    workflow_id: str, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Delete a workflow by its ID.

    This endpoint deletes a workflow from the system. Only the workflow owner or an admin
    can delete a workflow.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow to delete

    ## Response
    Returns a success message if the deletion was successful.

    ## Errors
    - 400: Bad request (validation failed)
    - 403: Permission denied (not authorized to delete this workflow)
    - 404: Workflow not found
    - 500: Server error
    """
    try:
        # Validate user
        user_details = await validate_user(current_user["user_id"])

        # Delete the workflow
        response = await workflow_service.delete_workflow(
            workflow_id=workflow_id, owner_details=user_details
        )

        handle_grpc_response_status(response)
        return DeleteWorkflowResponse(success=response.success, message=response.message)

    except Exception as e:
        print(f"[EXCEPTION] {e}")
        raise HTTPException(status_code=500, detail=str(e))


@workflow_router.get("", response_model=PaginatedWorkflowResponse)
async def list_workflows(
    page: int = Query(1, ge=1, description="Page number for pagination (starts at 1)"),
    page_size: int = Query(10, ge=1, le=100, description="Number of workflows per page (1-100)"),
    category: Optional[str] = Query(None, description="Filter by workflow category"),
    status: Optional[str] = Query(
        None, description="Filter by workflow status (active/inactive/draft)"
    ),
    visibility: Optional[str] = Query(
        None, description="Filter by workflow visibility (public/private)"
    ),
    search: Optional[str] = Query(
        None, description="Search term to filter workflows by name or description"
    ),
    tags: Optional[str] = Query(
        None,
        description="Filter by tags using comma-separated key:value pairs (e.g., 'department:sales,priority:high')",
    ),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Retrieve a paginated list of all workflows.

    This endpoint returns a list of workflows with pagination metadata.
    Users with "user" or "admin" roles can access this endpoint.

    ## Filters
    - **page**: Page number for pagination (starts at 1)
    - **page_size**: Number of workflows per page (1-100)
    - **category**: Filter by workflow category
    - **status**: Filter by workflow status (active/inactive/draft)
    - **visibility**: Filter by workflow visibility (public/private)
    - **search**: Search term to filter workflows by name or description
    - **tags**: Filter by tags using comma-separated key:value pairs

    ## Tag Format
    Tags should be provided as comma-separated key:value pairs, for example:
    `department:sales,priority:high,version:1.0`

    ## Response
    Returns a paginated list of workflows matching the filter criteria.

    ## Example
    ```
    GET /workflows?page=1&page_size=10&category=automation&status=active&search=customer&tags=priority:high
    ```
    """
    try:
        user_id = None if current_user.get("role") == "admin" else current_user.get("user_id")

        # Validate enum values if provided
        if category and category not in [c.value for c in WorkflowCategoryEnum]:
            raise HTTPException(status_code=400, detail=f"Invalid category: {category}")

        # Update to include "draft" status
        valid_statuses = [s.value for s in WorkflowStatusEnum] + ["draft"]
        if status and status not in valid_statuses:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid status: {status}. Valid values are: {', '.join(valid_statuses)}",
            )

        if visibility and visibility not in [v.value for v in WorkflowVisibilityEnum]:
            raise HTTPException(status_code=400, detail=f"Invalid visibility: {visibility}")

        # Parse tags if provided (format: "key1:value1,key2:value2")
        parsed_tags = None
        if tags:
            try:
                parsed_tags = {}
                for tag_pair in tags.split(","):
                    if ":" in tag_pair:
                        key, value = tag_pair.split(":", 1)
                        parsed_tags[key.strip()] = value.strip()
            except Exception as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid tags format. Use 'key1:value1,key2:value2': {str(e)}",
                )

        response = await workflow_service.list_workflows(
            page=page,
            page_size=page_size,
            category=category,
            status=status,
            visibility=visibility,
            search=search,
            tags=parsed_tags,
            user_id=user_id,
        )

        workflows = []
        for workflow in response.workflows:
            workflow_dict = prepare_workflow_dict(
                MessageToDict(workflow, preserving_proto_field_name=True)
            )

            # Convert JSON string fields to dictionaries as pydantic model requires
            if "tags" in workflow_dict and isinstance(workflow_dict["tags"], str):
                try:
                    workflow_dict["tags"] = json.loads(workflow_dict["tags"])
                except json.JSONDecodeError:
                    workflow_dict["tags"] = None

            workflows.append(WorkflowInDB.model_validate(workflow_dict))

        # Calculate pagination metadata
        total = response.total
        total_pages = response.total_pages
        current_page = page
        has_next_page = current_page < total_pages
        has_previous_page = current_page > 1

        metadata = PaginationMetadata(
            total=total,
            totalPages=total_pages,
            currentPage=current_page,
            pageSize=page_size,
            hasNextPage=has_next_page,
            hasPreviousPage=has_previous_page,
        )

        return PaginatedWorkflowResponse(data=workflows, metadata=metadata)
    except Exception as e:
        print(f"[ERROR] Unexpected error in list_workflows: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.post("/by-ids", response_model=WorkflowsByIdsApiResponse)
async def get_workflows_by_ids(
    request: WorkflowsByIdsRequestPayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Retrieves multiple workflows by their IDs.

    This endpoint allows fetching multiple workflows in a single request by providing a list of workflow IDs.

    ## Request Body
    - **ids**: List of workflow IDs to retrieve

    ## Response
    Returns a list of workflows matching the provided IDs and the total count.

    ## Example
    ```
    POST /workflows/by-ids
    {
        "ids": ["workflow-id-1", "workflow-id-2", "workflow-id-3"]
    }
    ```

    ## Errors
    - 400: Bad request (invalid input)
    - 500: Server error
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No workflow IDs provided")

        print(f"[DEBUG] Fetching workflows by IDs: {request.ids}")
        response = await workflow_service.get_workflows_by_ids(workflow_ids=request.ids)

        workflows = []
        for workflow in response.workflows:
            workflow_dict = prepare_workflow_dict(
                MessageToDict(workflow, preserving_proto_field_name=True)
            )

            # Parse JSON string tags to dict if it's a string
            if "tags" in workflow_dict and isinstance(workflow_dict["tags"], str):
                try:
                    workflow_dict["tags"] = json.loads(workflow_dict["tags"])
                except json.JSONDecodeError:
                    workflow_dict["tags"] = {}

            workflows.append(WorkflowInDB.model_validate(workflow_dict))

        return WorkflowsByIdsApiResponse(
            success=True,
            message=f"Retrieved {len(workflows)} workflows",
            workflows=workflows,
            total=len(workflows),
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error in get_workflows_by_ids: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.post(
    "/{workflow_id}/toggle-visibility", response_model=ToggleVisibilityResponseAPI
)
async def toggle_workflow_visibility_api(
    workflow_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Toggle the visibility of a workflow between PUBLIC and PRIVATE.

    DEPRECATED: Please use the /update-details/{workflow_id} endpoint with toggle_visibility=true instead.

    This endpoint allows users to switch a workflow's visibility setting with a single API call.
    If the workflow is currently PUBLIC, it will be set to PRIVATE, and vice versa.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow to toggle

    ## Response
    Returns the updated workflow with its new visibility setting if successful.

    ## Errors
    - 400: Bad request (validation failed)
    - 403: Permission denied (not authorized to modify this workflow)
    - 404: Workflow not found
    - 500: Server error
    """
    try:
        # Validate user
        user_details = await validate_user(current_user["user_id"])

        # user_details = {
        #     "id": "91a237fd-0225-4e02-9e9f-805eff073b07"
        # }

        # Toggle workflow visibility
        grpc_response = await workflow_service.toggle_workflow_visibility(
            workflow_id=workflow_id, owner_details=user_details
        )

        handle_grpc_response_status(grpc_response)

        # Parse workflow data
        workflow_dict = MessageToDict(grpc_response.workflow, preserving_proto_field_name=True)
        workflow_dict = parse_json_tags(workflow_dict)
        workflow_dict = prepare_workflow_dict(workflow_dict)

        return ToggleVisibilityResponseAPI(
            success=grpc_response.success,
            message=grpc_response.message,
            workflow=WorkflowInDB.model_validate(workflow_dict),
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in toggle_workflow_visibility_api: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.patch("/{workflow_id}", response_model=UpdateWorkflowResponse)
async def update_workflow_api(
    workflow_id: str,
    payload: WorkflowPatchPayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Update an existing workflow.

    DEPRECATED: Please use the /update-details/{workflow_id} endpoint instead.

    This endpoint allows users to update various aspects of a workflow including name,
    description, workflow data, start nodes, visibility, category, tags, and status.
    All fields are optional - only the provided fields will be updated.

    ## Request Body
    - **name**: Optional new name for the workflow
    - **description**: Optional new description
    - **workflow_data**: Optional updated workflow definition data
    - **start_node_data**: Optional updated start node data
    - **user_id**: Optional list of user ID with access to the workflow
    - **visibility**: Optional visibility setting (public/private)
    - **category**: Optional workflow category
    - **tags**: Optional key-value pairs for tagging the workflow
    - **status**: Optional workflow status
    - **version**: Optional version string
    - **is_changes_marketplace**: Optional marketplace flag

    ## Response
    Returns a success message if the update was successful.

    ## Errors
    - 400: Bad request (invalid input, validation failed)
    - 403: Permission denied (not authorized to update this workflow)
    - 404: Workflow not found
    - 500: Server error
    """
    try:

        user_id = None if current_user.get("role") == "admin" else current_user.get("user_id")

        # user_id = "91a237fd-0225-4e02-9e9f-805eff073b07"

        # Validate request data
        update_data = payload.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(status_code=400, detail="No fields provided for update.")

        # Update workflow
        grpc_response = await workflow_service.patch_workflow(
            workflow_id=workflow_id,
            update_fields=update_data,
            owner_details={"id": user_id},
        )

        handle_grpc_response_status(grpc_response)
        return UpdateWorkflowResponse(success=grpc_response.success, message=grpc_response.message)
    except HTTPException as http_exc:
        raise http_exc
    except ValueError as ve:
        print(f"[API ROUTE EXCEPTION] ValueError: {ve}")
        raise HTTPException(status_code=422, detail=str(ve))
    except Exception as e:
        print(f"[API ROUTE EXCEPTION] Unexpected error: {type(e).__name__} - {e}")
        raise HTTPException(status_code=500, detail=f"An internal server error occurred: {str(e)}")
