"""
Test one-to-one handle mapping constraint validation.

This test suite validates that the workflow schema converter properly enforces
the constraint that each target handle can only receive data from one source handle.
"""

import pytest
import sys
import os

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
    HandleMappingConflictError,
    _validate_one_to_one_handle_mapping
)


class TestOneToOneHandleMappingValidation:
    """Test suite for one-to-one handle mapping constraint validation."""

    def test_valid_one_to_one_mapping(self):
        """Test that valid one-to-one handle mappings pass validation."""
        edges = [
            {
                "id": "edge-1",
                "source": "node-1",
                "target": "node-2", 
                "sourceHandle": "output1",
                "targetHandle": "input1"
            },
            {
                "id": "edge-2",
                "source": "node-2",
                "target": "node-3",
                "sourceHandle": "output2", 
                "targetHandle": "input2"
            },
            {
                "id": "edge-3",
                "source": "node-1",
                "target": "node-3",
                "sourceHandle": "output3",
                "targetHandle": "input3"  # Different target handle, should be valid
            }
        ]
        
        # Should not raise any exception
        _validate_one_to_one_handle_mapping(edges)

    def test_multiple_sources_to_same_target_handle_raises_error(self):
        """Test that multiple sources connecting to the same target handle raises error."""
        edges = [
            {
                "id": "edge-1",
                "source": "node-1",
                "target": "node-3",
                "sourceHandle": "output1",
                "targetHandle": "input1"  # Same target handle
            },
            {
                "id": "edge-2", 
                "source": "node-2",
                "target": "node-3",
                "sourceHandle": "output2",
                "targetHandle": "input1"  # Same target handle - CONFLICT!
            }
        ]
        
        with pytest.raises(HandleMappingConflictError) as exc_info:
            _validate_one_to_one_handle_mapping(edges)
        
        error_message = str(exc_info.value)
        assert "Handle mapping constraint violation" in error_message
        assert "node-3.input1" in error_message
        assert "node-1.output1" in error_message
        assert "node-2.output2" in error_message
        assert "2 sources" in error_message

    def test_three_sources_to_same_target_handle(self):
        """Test error message with three sources connecting to same target handle."""
        edges = [
            {
                "id": "edge-1",
                "source": "node-1", 
                "target": "node-4",
                "sourceHandle": "result",
                "targetHandle": "data_input"
            },
            {
                "id": "edge-2",
                "source": "node-2",
                "target": "node-4", 
                "sourceHandle": "processed_data",
                "targetHandle": "data_input"  # Same target handle
            },
            {
                "id": "edge-3",
                "source": "node-3",
                "target": "node-4",
                "sourceHandle": "final_result", 
                "targetHandle": "data_input"  # Same target handle
            }
        ]
        
        with pytest.raises(HandleMappingConflictError) as exc_info:
            _validate_one_to_one_handle_mapping(edges)
        
        error_message = str(exc_info.value)
        assert "3 sources" in error_message
        assert "node-1.result" in error_message
        assert "node-2.processed_data" in error_message
        assert "node-3.final_result" in error_message

    def test_multiple_conflicts_in_same_workflow(self):
        """Test error message when multiple target handles have conflicts."""
        edges = [
            # First conflict: node-3.input1 has 2 sources
            {
                "id": "edge-1",
                "source": "node-1",
                "target": "node-3",
                "sourceHandle": "output1",
                "targetHandle": "input1"
            },
            {
                "id": "edge-2",
                "source": "node-2", 
                "target": "node-3",
                "sourceHandle": "output2",
                "targetHandle": "input1"
            },
            # Second conflict: node-4.data has 2 sources
            {
                "id": "edge-3",
                "source": "node-1",
                "target": "node-4",
                "sourceHandle": "result1",
                "targetHandle": "data"
            },
            {
                "id": "edge-4",
                "source": "node-3",
                "target": "node-4",
                "sourceHandle": "result2", 
                "targetHandle": "data"
            }
        ]
        
        with pytest.raises(HandleMappingConflictError) as exc_info:
            _validate_one_to_one_handle_mapping(edges)
        
        error_message = str(exc_info.value)
        assert "Found 2 conflicts" in error_message
        assert "node-3.input1" in error_message
        assert "node-4.data" in error_message

    def test_edges_without_handles_are_ignored(self):
        """Test that edges without sourceHandle/targetHandle are ignored (flow connections)."""
        edges = [
            # Valid handle connection
            {
                "id": "edge-1",
                "source": "node-1",
                "target": "node-2",
                "sourceHandle": "output1",
                "targetHandle": "input1"
            },
            # Flow connection without handles - should be ignored
            {
                "id": "edge-2",
                "source": "node-2",
                "target": "node-3"
                # No sourceHandle/targetHandle
            },
            # Another flow connection - should be ignored
            {
                "id": "edge-3", 
                "source": "node-3",
                "target": "node-4",
                "sourceHandle": None,
                "targetHandle": None
            }
        ]
        
        # Should not raise any exception
        _validate_one_to_one_handle_mapping(edges)

    def test_same_target_handle_different_nodes_is_valid(self):
        """Test that same handle name on different target nodes is valid."""
        edges = [
            {
                "id": "edge-1",
                "source": "node-1",
                "target": "node-2",
                "sourceHandle": "output",
                "targetHandle": "input"  # Same handle name
            },
            {
                "id": "edge-2",
                "source": "node-1", 
                "target": "node-3",
                "sourceHandle": "output",
                "targetHandle": "input"  # Same handle name but different target node
            }
        ]
        
        # Should not raise any exception - different target nodes
        _validate_one_to_one_handle_mapping(edges)

    def test_full_workflow_conversion_with_valid_mapping(self):
        """Test full workflow conversion with valid one-to-one mappings."""
        workflow_data = {
            "nodes": [
                {
                    "id": "node-1",
                    "data": {
                        "type": "mcp",
                        "definition": {
                            "name": "TestNode1",
                            "outputs": [{"name": "result", "output_type": "string"}]
                        }
                    }
                },
                {
                    "id": "node-2", 
                    "data": {
                        "type": "mcp",
                        "definition": {
                            "name": "TestNode2",
                            "inputs": [{"name": "data", "input_type": "string", "is_handle": True}],
                            "outputs": [{"name": "processed", "output_type": "string"}]
                        }
                    }
                }
            ],
            "edges": [
                {
                    "id": "edge-1",
                    "source": "node-1",
                    "target": "node-2",
                    "sourceHandle": "result",
                    "targetHandle": "data"
                }
            ],
            "mcp_configs": []
        }
        
        # Should convert successfully without raising exception
        result = convert_workflow_to_transition_schema(workflow_data)
        assert "transitions" in result
        assert "nodes" in result

    def test_full_workflow_conversion_with_conflict_raises_error(self):
        """Test full workflow conversion with handle mapping conflict raises error."""
        workflow_data = {
            "nodes": [
                {
                    "id": "node-1",
                    "data": {
                        "type": "mcp", 
                        "definition": {
                            "name": "TestNode1",
                            "outputs": [{"name": "result1", "output_type": "string"}]
                        }
                    }
                },
                {
                    "id": "node-2",
                    "data": {
                        "type": "mcp",
                        "definition": {
                            "name": "TestNode2", 
                            "outputs": [{"name": "result2", "output_type": "string"}]
                        }
                    }
                },
                {
                    "id": "node-3",
                    "data": {
                        "type": "mcp",
                        "definition": {
                            "name": "TestNode3",
                            "inputs": [{"name": "input_data", "input_type": "string", "is_handle": True}]
                        }
                    }
                }
            ],
            "edges": [
                {
                    "id": "edge-1",
                    "source": "node-1",
                    "target": "node-3",
                    "sourceHandle": "result1",
                    "targetHandle": "input_data"
                },
                {
                    "id": "edge-2",
                    "source": "node-2", 
                    "target": "node-3",
                    "sourceHandle": "result2",
                    "targetHandle": "input_data"  # CONFLICT: Same target handle!
                }
            ],
            "mcp_configs": []
        }
        
        # Should raise HandleMappingConflictError during conversion
        with pytest.raises(HandleMappingConflictError) as exc_info:
            convert_workflow_to_transition_schema(workflow_data)
        
        error_message = str(exc_info.value)
        assert "node-3.input_data" in error_message
        assert "2 sources" in error_message

    def test_error_message_includes_edge_ids(self):
        """Test that error message includes edge IDs for traceability."""
        edges = [
            {
                "id": "connection-abc-123",
                "source": "source-node",
                "target": "target-node",
                "sourceHandle": "output_field",
                "targetHandle": "input_field"
            },
            {
                "id": "connection-def-456",
                "source": "another-source",
                "target": "target-node", 
                "sourceHandle": "another_output",
                "targetHandle": "input_field"  # Same target handle
            }
        ]
        
        with pytest.raises(HandleMappingConflictError) as exc_info:
            _validate_one_to_one_handle_mapping(edges)
        
        error_message = str(exc_info.value)
        assert "connection-abc-123" in error_message
        assert "connection-def-456" in error_message

    def test_empty_edges_list(self):
        """Test that empty edges list doesn't raise error."""
        edges = []
        
        # Should not raise any exception
        _validate_one_to_one_handle_mapping(edges)

    def test_single_edge_is_valid(self):
        """Test that single edge is always valid."""
        edges = [
            {
                "id": "edge-1",
                "source": "node-1",
                "target": "node-2",
                "sourceHandle": "output",
                "targetHandle": "input"
            }
        ]
        
        # Should not raise any exception
        _validate_one_to_one_handle_mapping(edges)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
