from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.routers import (
    admin_routes,
    agent_routes,
    agent_graph_routes,
    agent_template_routes,
    agent_avatar_routes,
    analytics_routes,
    api_key_routes,
    application_routes,
    communication_routes,
    google_drive_routes,
    health_routes,
    livekit_routes,
    notification_routes,
    organisation_routes,
    prompt_routes,
    user_routes,
    waitlist_routes,
    workflow_routes,
    mcp_routes,
    credential_routes,
    marketplace_routes,
    workflow_builder_routes,
    github_routes,
)

from app.core.config import settings

app = FastAPI(title=settings.APP_NAME, openapi_url=f"{settings.API_V1_STR}/openapi.json")

# Set up CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS.split(","),
    allow_credentials=settings.CORS_CREDENTIALS,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(user_routes.auth_router, prefix=settings.API_V1_STR)
app.include_router(user_routes.user_router, prefix=settings.API_V1_STR)
app.include_router(admin_routes.admin_auth_router, prefix=settings.API_V1_STR)
app.include_router(admin_routes.admin_router, prefix=settings.API_V1_STR)
app.include_router(admin_routes.role_router, prefix=settings.API_V1_STR)
app.include_router(notification_routes.notification_router, prefix=settings.API_V1_STR)
app.include_router(communication_routes.communication_router, prefix=settings.API_V1_STR)
app.include_router(workflow_routes.workflow_router, prefix=settings.API_V1_STR)

app.include_router(waitlist_routes.waitlist_router, prefix=settings.API_V1_STR)
app.include_router(organisation_routes.organisation_router, prefix=settings.API_V1_STR)
app.include_router(api_key_routes.api_key_router, prefix=settings.API_V1_STR)
app.include_router(livekit_routes.livekit_router, prefix=settings.API_V1_STR)
app.include_router(agent_routes.agent_router, prefix=settings.API_V1_STR)
app.include_router(agent_graph_routes.agent_graph_router, prefix=settings.API_V1_STR)
app.include_router(agent_template_routes.template_router, prefix=settings.API_V1_STR)
app.include_router(agent_avatar_routes.avatar_router, prefix=settings.API_V1_STR)
app.include_router(mcp_routes.mcp_router, prefix=settings.API_V1_STR)
app.include_router(mcp_routes.mcp_marketplace, prefix=settings.API_V1_STR)
app.include_router(credential_routes.credential_router, prefix=settings.API_V1_STR)
app.include_router(marketplace_routes.marketplace_router, prefix=settings.API_V1_STR)
app.include_router(health_routes.health_router, prefix=settings.API_V1_STR)
app.include_router(workflow_builder_routes.workflow_builder_router, prefix=settings.API_V1_STR)
app.include_router(prompt_routes.prompt_router, prefix=settings.API_V1_STR)
app.include_router(google_drive_routes.google_drive_router, prefix=settings.API_V1_STR)
app.include_router(github_routes.auth_router, prefix=settings.API_V1_STR)
app.include_router(analytics_routes.analytics_router, prefix=settings.API_V1_STR)
app.include_router(application_routes.application_router, prefix=settings.API_V1_STR)


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


# # Add shutdown event handler
# @app.on_event("shutdown")
# async def on_shutdown():
#     await shutdown_sse_connections()
