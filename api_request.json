 "ApiRequestNode": {
      "name": "ApiRequestNode",
      "display_name": "API Request",
      "description": "Makes a single HTTP request to the specified URL.",
      "category": "Data Interaction",
      "icon": "Globe",
      "beta": false,
      "requires_approval": false,
      "inputs": [
        {
          "name": "url",
          "display_name": "URL",
          "info": "The URL to make the request to.",
          "input_type": "string",
          "input_types": null,
          "required": true,
          "is_handle": true,
          "is_list": false,
          "real_time_refresh": false,
          "advanced": true,
          "value": "",
          "options": null,
          "visibility_rules": null,
          "visibility_logic": "OR",
          "requirement_rules": null,
          "requirement_logic": "OR"
        },
        {
          "name": "method",
          "display_name": "HTTP Method",
          "info": "The HTTP method to use for the request.",
          "input_type": "dropdown",
          "input_types": null,
          "required": false,
          "is_handle": false,
          "is_list": false,
          "real_time_refresh": true,
          "advanced": false,
          "value": "GET",
          "options": [
            "GET",
            "POST",
            "PUT",
            "PATCH",
            "DELETE"
          ],
          "visibility_rules": null,
          "visibility_logic": "OR",
          "requirement_rules": null,
          "requirement_logic": "OR"
        },
        {
          "name": "query_params",
          "display_name": "Query Parameters",
          "info": "Key-value pairs to append to the URL query string (optional).",
          "input_type": "dict",
          "input_types": null,
          "required": false,
          "is_handle": true,
          "is_list": false,
          "real_time_refresh": false,
          "advanced": true,
          "value": {},
          "options": null,
          "visibility_rules": null,
          "visibility_logic": "OR",
          "requirement_rules": null,
          "requirement_logic": "OR"
        },
        {
          "name": "headers",
          "display_name": "Request Headers",
          "info": "Key-value pairs for request headers (optional).",
          "input_type": "dict",
          "input_types": null,
          "required": false,
          "is_handle": true,
          "is_list": false,
          "real_time_refresh": false,
          "advanced": true,
          "value": {},
          "options": null,
          "visibility_rules": null,
          "visibility_logic": "OR",
          "requirement_rules": null,
          "requirement_logic": "OR"
        },
        {
          "name": "body",
          "display_name": "Request Body (JSON)",
          "info": "Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized.",
          "input_type": "dict",
          "input_types": null,
          "required": false,
          "is_handle": true,
          "is_list": false,
          "real_time_refresh": false,
          "advanced": true,
          "value": {},
          "options": null,
          "visibility_rules": [
            {
              "field_name": "method",
              "field_value": "POST",
              "operator": "equals"
            },
            {
              "field_name": "method",
              "field_value": "PUT",
              "operator": "equals"
            },
            {
              "field_name": "method",
              "field_value": "PATCH",
              "operator": "equals"
            }
          ],
          "visibility_logic": "OR",
          "requirement_rules": null,
          "requirement_logic": "OR"
        },
        {
          "name": "timeout",
          "display_name": "Timeout (seconds)",
          "info": "Maximum time to wait for a response.",
          "input_type": "int",
          "input_types": null,
          "required": false,
          "is_handle": false,
          "is_list": false,
          "real_time_refresh": false,
          "advanced": true,
          "value": 10,
          "options": null,
          "visibility_rules": null,
          "visibility_logic": "OR",
          "requirement_rules": null,
          "requirement_logic": "OR"
        },
        {
          "name": "follow_redirects",
          "display_name": "Follow Redirects",
          "info": "Automatically follow HTTP redirects (e.g., 301, 302).",
          "input_type": "bool",
          "input_types": null,
          "required": false,
          "is_handle": false,
          "is_list": false,
          "real_time_refresh": false,
          "advanced": true,
          "value": true,
          "options": null,
          "visibility_rules": null,
          "visibility_logic": "OR",
          "requirement_rules": null,
          "requirement_logic": "OR"
        },
        {
          "name": "save_to_file",
          "display_name": "Save Response to File",
          "info": "Save response body to a temporary file instead of returning content directly. 'Result' output will be the file path.",
          "input_type": "bool",
          "input_types": null,
          "required": false,
          "is_handle": false,
          "is_list": false,
          "real_time_refresh": false,
          "advanced": true,
          "value": false,
          "options": null,
          "visibility_rules": null,
          "visibility_logic": "OR",
          "requirement_rules": null,
          "requirement_logic": "OR"
        },
        {
          "name": "output_format",
          "display_name": "Output Format",
          "info": "'auto': Try JSON, fallback to text. 'json': Force JSON parsing (error if invalid). 'text': Return as text. 'bytes': Return raw bytes. 'file_path': Always save to file (requires 'Save Response to File'). 'metadata_dict': Return dict with status, headers, body/path, error.",
          "input_type": "dropdown",
          "input_types": null,
          "required": false,
          "is_handle": false,
          "is_list": false,
          "real_time_refresh": false,
          "advanced": true,
          "value": "auto",
          "options": [
            "auto",
            "json",
            "text",
            "bytes",
            "file_path",
            "metadata_dict"
          ],
          "visibility_rules": null,
          "visibility_logic": "OR",
          "requirement_rules": null,
          "requirement_logic": "OR"
        },
        {
          "name": "raise_on_error",
          "display_name": "Raise Exception on HTTP Error",
          "info": "Stop workflow execution if an HTTP error status (4xx, 5xx) is received.",
          "input_type": "bool",
          "input_types": null,
          "required": false,
          "is_handle": false,
          "is_list": false,
          "real_time_refresh": false,
          "advanced": true,
          "value": false,
          "options": null,
          "visibility_rules": null,
          "visibility_logic": "OR",
          "requirement_rules": null,
          "requirement_logic": "OR"
        }
      ],
      "outputs": [
        {
          "name": "result",
          "display_name": "Result",
          "output_type": "Any",
          "method": null
        },
        {
          "name": "status_code",
          "display_name": "Status Code",
          "output_type": "int",
          "method": null
        },
        {
          "name": "response_headers",
          "display_name": "Response Headers",
          "output_type": "dict",
          "method": null
        },
        {
          "name": "error",
          "display_name": "Error",
          "output_type": "str",
          "method": null
        }
      ],
      "is_valid": true,
      "path": "components.data interaction.apirequestnode",
      "interface_issues": []
    },