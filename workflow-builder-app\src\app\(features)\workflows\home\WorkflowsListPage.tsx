"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter } from "next/navigation";
import { getClientAccessToken } from "@/lib/clientCookies";
import {
  fetchWorkflowsByUser,
  createEmptyWorkflow,
  WorkflowSummary,
  WorkflowDetails,
} from "../api";
import { useUserStore } from "@/store/userStore";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Pagination,
  PaginationContent,
  Pa<PERSON>ationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Workflow,
  Plus,
  Search,
  AlertCircle,
  Loader2,
  ChevronDown,
  FileEdit,
  File,
  FileUp,
} from "lucide-react";
import WorkflowCard from "../components/WorkflowCard";
import Image from "next/image";

export default function WorkflowsListPage() {
  const router = useRouter();
  const user = useUserStore((state) => state.user);

  const [workflows, setWorkflows] = useState<WorkflowDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState<string>("updated_desc");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isCreatingWorkflow, setIsCreatingWorkflow] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  // Check authentication status on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get access token from client-side cookie
        const cookieToken = getClientAccessToken();

        // If no access token in cookie or store, redirect to login
        if (!cookieToken && (!user || !user.accessToken)) {
          console.error("Authentication error: No access token available");
          setError("User not authenticated");
          setIsLoading(false);

          // Redirect to login page after a short delay
          setTimeout(() => {
            window.location.href = "/login";
          }, 2000);

          return;
        }

        // If we have a token in cookie but not in store, update the store
        if (cookieToken && (!user || !user.accessToken)) {
          console.log("Found token in cookie but not in store, updating store");
          // This is a temporary fix - ideally we would fetch user details here
          useUserStore.getState().setUser({
            accessToken: cookieToken,
            // We don't have other user details, but at least we have the token
          });
        }
      } catch (error) {
        console.error("Error checking authentication:", error);
        setError("Authentication error");
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [user]);

  // Fetch workflows when component mounts or pagination changes
  useEffect(() => {
    const fetchWorkflows = async () => {
      // Get token from store or cookie
      const tokenFromStore = user?.accessToken;
      const tokenFromCookie = getClientAccessToken();
      const accessToken = tokenFromStore || tokenFromCookie;

      // Skip if no access token available
      if (!accessToken) {
        console.error("No access token available for API request");
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Pass the access token explicitly to the API function
        const response = await fetchWorkflowsByUser(currentPage, pageSize, accessToken);

        setWorkflows(response?.data);

        // Update pagination state
        if (response.metadata) {
          setTotalPages(response.metadata.totalPages || 1);
          setCurrentPage(response.metadata.currentPage || 1);
        }
      } catch (err) {
        console.error("Failed to fetch workflows:", err);
        // Provide more specific error messages based on the error
        if (err instanceof Error) {
          if (err.message.includes("401") || err.message.includes("403")) {
            setError("Authentication failed. Please log in again.");
          } else if (err.message.includes("404")) {
            setError("No workflows found for this user.");
          } else if (err.message.includes("500")) {
            setError("Server error. Please try again later.");
          } else {
            setError(`Failed to load workflows: ${err.message}`);
          }
        } else {
          setError("Failed to load workflows. Please try again later.");
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorkflows();
  }, [user, currentPage, pageSize]);

  // Helper function to get workflow data regardless of structure
  const getWorkflowData = (data: any): WorkflowDetails => {
    // If it's a WorkflowSummary (has workflow property), return the nested workflow
    if ("workflow" in data && data.workflow) {
      return data.workflow as WorkflowDetails;
    }
    // Otherwise, it's already a WorkflowDetails
    return data as WorkflowDetails;
  };

  // Sort workflows based on selected option
  const sortedWorkflows = useMemo(() => {
    if (!workflows) return [];

    // Filter by search term if provided
    let filtered = workflows;
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = workflows.filter((wf) => {
        const workflowData = getWorkflowData(wf);
        return (
          workflowData.name?.toLowerCase().includes(term) ||
          (workflowData.description && workflowData.description.toLowerCase().includes(term))
        );
      });
    }

    // Sort based on selected option
    return [...filtered].sort((a, b) => {
      const aData = getWorkflowData(a);
      const bData = getWorkflowData(b);

      switch (sortOption) {
        case "updated_desc":
          return (
            new Date(bData.updated_at || "").getTime() - new Date(aData.updated_at || "").getTime()
          );
        case "updated_asc":
          return (
            new Date(aData.updated_at || "").getTime() - new Date(bData.updated_at || "").getTime()
          );
        case "name_asc":
          return (aData.name || "").localeCompare(bData.name || "");
        case "name_desc":
          return (bData.name || "").localeCompare(aData.name || "");
        default:
          return (
            new Date(bData.updated_at || "").getTime() - new Date(aData.updated_at || "").getTime()
          );
      }
    });
  }, [workflows, sortOption, searchTerm]);

  // Handle creating a new workflow
  const handleCreateWorkflow = async () => {
    try {
      setIsCreatingWorkflow(true);
      const newWorkflow = await createEmptyWorkflow();
      console.log("Created new workflow:", newWorkflow);
      // Redirect to canvas page with the new workflow ID
      // router.push(`/workflows/${newWorkflow.workflow_id}`);
      router.push(`/?workflow_id=${newWorkflow.workflow_id}`);
    } catch (err) {
      console.error("Failed to create workflow:", err);
      setError("Failed to create a new workflow. Please try again.");
      setIsCreatingWorkflow(false);
    }
  };

  // Handle creating workflow from template (placeholder)
  const handleCreateFromTemplate = () => {
    // TODO: Implement template selection functionality
    console.log("Create from template clicked - functionality to be implemented");
  };

  // Handle importing workflow file (placeholder)
  const handleImportFile = () => {
    // TODO: Implement file import functionality
    console.log("Import file clicked - functionality to be implemented");
  };

  // Memoize the handleSelectWorkflow function to prevent unnecessary re-renders
  const handleSelectWorkflow = useCallback(
    (workflow: WorkflowSummary | WorkflowDetails) => {
      // Check if the workflow is a WorkflowSummary (has workflow property) or a direct WorkflowDetails
      const workflowId =
        "workflow" in workflow && workflow.workflow
          ? workflow.workflow.id
          : (workflow as WorkflowDetails).id;

      if (workflowId) {
        // router.push(`/workflows/${workflowId}`);
        router.push(`/?workflow_id=${workflowId}`);
      }
    },
    [router],
  );

  return (
    <main className="bg-background min-h-screen">
      {/* Header */}
      <div className="bg-brand-header-bg text-brand-white-text p-6 shadow-md">
        <div
          className="container"
          style={{
            maxWidth: "100%",
          }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Image
                  src="/wflogo_white.svg"
                  alt="Workflow Builder Logo"
                  width={120}
                  height={30}
                  className="h-8 w-auto"
                />
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  disabled={isCreatingWorkflow}
                  className="bg-brand-primary hover:bg-brand-primary/90 text-white"
                >
                  {isCreatingWorkflow ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      Create New Workflow
                      <ChevronDown className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem
                  onClick={handleCreateWorkflow}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <FileEdit className="h-4 w-4" />
                  <span>Create from blank</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleCreateFromTemplate}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <File className="h-4 w-4" />
                  <span>Create from template</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleImportFile}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <FileUp className="h-4 w-4" />
                  <span>Import file</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div
        className="bg-brand-background container px-4 py-8"
        style={{
          maxWidth: "100%",
          background: "black",
          padding: "20px 100px",
        }}
      >
        {/* Filters and search */}
        <div className="mb-6 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
          <div className="relative w-full md:w-64">
            <Search
              className="text-brand-secondary-font absolute top-3 left-3 h-4 w-4"
              aria-hidden="true"
            />
            <Input
              placeholder="Search workflows..."
              className="border-brand-stroke focus-visible:ring-brand-primary/20 pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              aria-label="Search workflows"
              id="workflow-search"
              name="workflow-search"
            />
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-brand-secondary-font font-secondary text-sm">Sort by:</span>
              <Select value={sortOption} onValueChange={setSortOption}>
                <SelectTrigger className="border-brand-stroke focus:ring-brand-primary/20 w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent className="border-brand-border-color bg-brand-card">
                  <SelectItem value="updated_desc">Latest Update</SelectItem>
                  <SelectItem value="updated_asc">Oldest Update</SelectItem>
                  <SelectItem value="name_asc">Name (A-Z)</SelectItem>
                  <SelectItem value="name_desc">Name (Z-A)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-brand-secondary-font font-secondary text-sm">Show:</span>
              <Select
                value={pageSize.toString()}
                onValueChange={(value) => {
                  setPageSize(Number(value));
                  setCurrentPage(1); // Reset to first page when changing page size
                }}
              >
                <SelectTrigger className="border-brand-stroke focus:ring-brand-primary/20 w-[80px]">
                  <SelectValue placeholder="Page size" />
                </SelectTrigger>
                <SelectContent className="border-brand-border-color bg-brand-card">
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Workflows grid */}
        {isLoading ? (
          <div className="flex h-64 items-center justify-center">
            <Loader2 className="text-brand-primary h-8 w-8 animate-spin" />
            <span className="font-primary ml-2 text-lg">Loading workflows...</span>
          </div>
        ) : error ? (
          <div className="flex h-64 flex-col items-center justify-center text-center">
            <AlertCircle className="text-brand-unpublish mb-4 h-12 w-12" />
            <h3 className="font-primary mb-2 text-xl font-semibold">Failed to Load Workflows</h3>
            <p className="text-brand-secondary-font mb-4">{error}</p>
            <div className="flex gap-4">
              <Button
                onClick={() => window.location.reload()}
                className="bg-brand-primary hover:bg-brand-primary/90 text-white"
              >
                Try Again
              </Button>
              {error.includes("authenticated") && (
                <Button
                  variant="outline"
                  onClick={() => (window.location.href = "/login")}
                  className="border-brand-border-color text-brand-primary hover:bg-brand-clicked"
                >
                  Go to Login
                </Button>
              )}
            </div>
          </div>
        ) : sortedWorkflows.length === 0 ? (
          <div className="flex h-64 flex-col items-center justify-center text-center">
            <Workflow className="text-brand-secondary mb-4 h-12 w-12" />
            <h3 className="font-primary mb-2 text-xl font-semibold">No Workflows Found</h3>
            <p className="text-brand-secondary-font mb-4">
              {searchTerm
                ? "No workflows match your search criteria."
                : "You don't have any workflows yet. Create your first workflow to get started."}
            </p>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  disabled={isCreatingWorkflow}
                  className="bg-brand-primary hover:bg-brand-primary/90 text-white"
                >
                  {isCreatingWorkflow ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      Create New Workflow
                      <ChevronDown className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center" className="w-56">
                <DropdownMenuItem
                  onClick={handleCreateWorkflow}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <FileEdit className="h-4 w-4" />
                  <span>Create from blank</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleCreateFromTemplate}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <File className="h-4 w-4" />
                  <span>Create from template</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleImportFile}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <FileUp className="h-4 w-4" />
                  <span>Import file</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        ) : (
          <div className="flex flex-col gap-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {sortedWorkflows.map((workflow) => {
                // Generate a key that works for both data structures
                const workflowData = getWorkflowData(workflow);
                const key = workflowData.id || Math.random().toString();

                return (
                  <WorkflowCard key={key} workflow={workflow} onClick={handleSelectWorkflow} />
                );
              })}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <Pagination className="mt-6" aria-label="Workflow pagination">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      size="default"
                      onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                      className={`${currentPage <= 1 ? "pointer-events-none opacity-50" : ""} text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke`}
                      aria-disabled={currentPage <= 1}
                    />
                  </PaginationItem>

                  {/* First page */}
                  {currentPage > 2 && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => setCurrentPage(1)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        1
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Ellipsis if needed */}
                  {currentPage > 3 && (
                    <PaginationItem>
                      <PaginationEllipsis className="text-brand-secondary-font" />
                    </PaginationItem>
                  )}

                  {/* Previous page if not first */}
                  {currentPage > 1 && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => setCurrentPage(currentPage - 1)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        {currentPage - 1}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Current page */}
                  <PaginationItem>
                    <PaginationLink
                      size="default"
                      isActive
                      className="brand-gradient-indicator text-brand-white-text border-none"
                    >
                      {currentPage}
                    </PaginationLink>
                  </PaginationItem>

                  {/* Next page if not last */}
                  {currentPage < totalPages && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => setCurrentPage(currentPage + 1)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        {currentPage + 1}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Ellipsis if needed */}
                  {currentPage < totalPages - 2 && (
                    <PaginationItem>
                      <PaginationEllipsis className="text-brand-secondary-font" />
                    </PaginationItem>
                  )}

                  {/* Last page */}
                  {currentPage < totalPages - 1 && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => setCurrentPage(totalPages)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        {totalPages}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      size="default"
                      onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                      className={`${currentPage >= totalPages ? "pointer-events-none opacity-50" : ""} text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke`}
                      aria-disabled={currentPage >= totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </div>
        )}
      </div>
    </main>
  );
}
