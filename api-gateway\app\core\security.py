from datetime import datetime, timedelta
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIKeyHeader
from jose import JWTError, jwt
from app.core.config import settings
from dotenv import load_dotenv


load_dotenv()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/users/login")


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


def create_refresh_token(data: dict) -> str:
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme)) -> dict:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        if not payload.get("sub"):
            raise credentials_exception
        return payload
    except JWTError:
        raise credentials_exception


# 1. Server Authentication Header
server_auth_header = APIKeyHeader(
    name="X-Server-Auth-Key",
    scheme_name="ServerAuth",  # <--- Unique scheme name for OpenAPI/Swagger
    auto_error=False,
)


def validate_server_auth_key(server_auth_key: str = Depends(server_auth_header)) -> bool:
    """
    Validate the server authentication key for get_workflow_by_id route (Workflow Service).

    Args:
        server_auth_key (str): The authentication key provided in the request header.

    Returns:
        bool: Returns True if the authentication key is valid.

    Raises:
        HTTPException: If the authentication key is missing, invalid, or not configured.
    """
    # Retrieve the expected server authentication key from environment variables
    EXPECTED_SERVER_AUTH_KEY = settings.ORCHESTRATION_SERVER_AUTH_KEY

    if not server_auth_key or server_auth_key != EXPECTED_SERVER_AUTH_KEY:
        raise HTTPException(
            status_code=403,
            detail="Invalid or missing server authentication key (X-Server-Auth-Key)",
        )
    return True


# Define the header for Agent Platform authentication
agent_platform_auth_header = APIKeyHeader(name="X-Agent-Platform-Auth-Key", auto_error=False)


# --- Dependency to Validate Agent Platform Auth Key (Agent Platform) ---
def validate_agent_platform_auth_key(
    agent_platform_auth_key: str = Depends(agent_platform_auth_header),
) -> bool:
    """
    Validate the agent platform authentication key for get_agent_by_id route (Agent Platform Service).

    Args:
        agent_platform_auth_key (str): The authentication key provided in the request header.

    Returns:
        bool: Returns True if the authentication key is valid.

    Raises:
        HTTPException: If the authentication key is missing or invalid.
    """
    # Retrieve the expected agent platform authentication key from environment variables
    EXPECTED_AGENT_PLATFORM_AUTH_KEY = settings.AGENT_PLATFORM_AUTH_KEY

    # Check if the key is present and matches the expected key
    if not agent_platform_auth_key or agent_platform_auth_key != EXPECTED_AGENT_PLATFORM_AUTH_KEY:
        raise HTTPException(
            status_code=403, detail="Invalid or missing agent platform authentication key"
        )

    return True


# 3. Workflow Service Authentication Header
workflow_service_auth_header = APIKeyHeader(
    name="X-Workflow-Service-Auth-Key",
    scheme_name="WorkflowServiceAuth",  # <--- Unique scheme name for OpenAPI/Swagger
    auto_error=False,
)


def validate_workflow_service_auth_key(
    workflow_auth_key: str = Depends(workflow_service_auth_header),
) -> bool:
    """
    Validate the workflow service authentication key for get_mcp_config_for_workflow_service route.

    Args:
        workflow_auth_key (str): The authentication key provided in the request header.

    Returns:
        bool: Returns True if the authentication key is valid.

    Raises:
        HTTPException: If the authentication key is missing or invalid.
    """
    EXPECTED_WORKFLOW_SERVICE_AUTH_KEY = settings.WORKFLOW_SERVICE_AUTH_KEY

    print(f"Workflow auth key: {EXPECTED_WORKFLOW_SERVICE_AUTH_KEY}")
    print(f"Workflow auth key 2: {workflow_auth_key}")

    if not EXPECTED_WORKFLOW_SERVICE_AUTH_KEY:
        print("Warning: WORKFLOW_SERVICE_AUTH_KEY environment variable not set.")
        raise HTTPException(
            status_code=500, detail="Workflow Service auth key configuration missing"
        )

    if not workflow_auth_key or workflow_auth_key != EXPECTED_WORKFLOW_SERVICE_AUTH_KEY:
        raise HTTPException(
            status_code=403,
            detail="Invalid or missing workflow service authentication key (X-Workflow-Service-Auth-Key)",
        )
    return True


# 4. Deployment Worker Authentication Header
deployment_worker_auth_header = APIKeyHeader(
    name="X-Deployment-Worker-Auth-Key",
    scheme_name="DeploymentWorkerAuth",
    auto_error=False,
)


def validate_deployment_worker_auth_key(
    deployment_worker_auth_key: str = Depends(deployment_worker_auth_header),
) -> bool:
    """
    Validate the deployment worker authentication key update_deployment_status route.

    Args:
        workflow_auth_key (str): The authentication key provided in the request header.

    Returns:
        bool: Returns True if the authentication key is valid.

    Raises:
        HTTPException: If the authentication key is missing or invalid.
    """
    EXPECTED_DEPLOYMENT_WORKER_AUTH_KEY = settings.DEPLOYMENT_WORKER_AUTH_KEY

    print(f"Workflow auth key: {EXPECTED_DEPLOYMENT_WORKER_AUTH_KEY}")

    if not EXPECTED_DEPLOYMENT_WORKER_AUTH_KEY:
        print("Warning: WORKFLOW_SERVICE_AUTH_KEY environment variable not set.")
        raise HTTPException(status_code=500, detail="auth key configuration missing")

    if (
        not deployment_worker_auth_key
        or deployment_worker_auth_key != EXPECTED_DEPLOYMENT_WORKER_AUTH_KEY
    ):
        raise HTTPException(
            status_code=403,
            detail="Invalid or missing deployment worker authentication key (X-Deployment-Worker-Auth-Key)",
        )
    return True

# Organisation Authentication Header
org_auth_header = APIKeyHeader(
    name="X-Org-Auth-Key",
    scheme_name="OrgAuth",
    auto_error=False,
)

def validate_org_auth_key(server_auth_key: str = Depends(org_auth_header)) -> bool:
    """
    Validate the agent authentication key for semantic search route (Organisation Service).

    Args:
        server_auth_key (str): The authentication key provided in the request header.

    Returns:
        bool: Returns True if the authentication key is valid.

    Raises:
        HTTPException: If the authentication key is missing, invalid, or not configured.
    """
    # Retrieve the expected server authentication key from environment variables
    EXPECTED_SERVER_AUTH_KEY = settings.ORGANISATION_SERVICE_AUTH_KEY

    if not server_auth_key or server_auth_key != EXPECTED_SERVER_AUTH_KEY:
        raise HTTPException(
            status_code=403,
            detail="Invalid or missing organisation authentication key (X-Org-Auth-Key)",
        )
    return True
