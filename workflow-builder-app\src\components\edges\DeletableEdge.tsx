import React, { useCallback } from "react";
import { EdgeProps, getBezierPath, EdgeLabelRenderer, useReactFlow } from "reactflow";
import { Trash2 } from "lucide-react";

const DeletableEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
  selected,
}) => {
  const { setEdges } = useReactFlow();

  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const onEdgeClick = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation();
      setEdges((edges) => edges.filter((edge) => edge.id !== id));
    },
    [id, setEdges],
  );

  // Enhanced styling for the edge
  const edgeStyle = {
    strokeWidth: selected ? 3 : style?.strokeWidth || 2,
    stroke: selected ? "#ff6b6b" : style?.stroke || "var(--primary)",
    fill: "none",
    ...style,
  };

  return (
    <>
      {/* Custom SVG path */}
      <path
        id={id}
        d={edgePath}
        style={edgeStyle}
        className="react-flow__edge-path"
        markerEnd={markerEnd}
      />
      <EdgeLabelRenderer>
        {/* Always show delete icon */}
        <div
          style={{
            position: "absolute",
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            pointerEvents: "all",
            zIndex: 1000,
          }}
          className="nodrag nopan"
        >
          <button
            className="flex h-3 w-3 items-center justify-center rounded-full bg-white text-gray-600 shadow-sm transition-all duration-200 hover:scale-110 hover:bg-gray-100"
            onClick={onEdgeClick}
            title="Delete edge"
            type="button"
          >
            <Trash2 className="h-2 w-2" />
          </button>
        </div>
      </EdgeLabelRenderer>
    </>
  );
};

export default DeletableEdge;
