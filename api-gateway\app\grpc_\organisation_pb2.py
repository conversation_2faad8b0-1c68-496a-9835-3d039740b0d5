# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: organisation.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'organisation.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12organisation.proto\x12\x0corganisation\"\x8d\x01\n\x19\x43reateOrganisationRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bwebsite_url\x18\x02 \x01(\t\x12\x10\n\x08industry\x18\x03 \x01(\t\x12\x12\n\ncreated_by\x18\x04 \x01(\t\x12\x12\n\nadmin_name\x18\x05 \x01(\t\x12\x13\n\x0b\x61\x64min_email\x18\x06 \x01(\t\"$\n\x16GetOrganisationRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\x90\x01\n\x11OrganisationModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0bwebsite_url\x18\x03 \x01(\t\x12\x10\n\x08industry\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x05 \x01(\t\x12\x12\n\ncreated_at\x18\x06 \x01(\t\x12\x12\n\nupdated_at\x18\x07 \x01(\t\"\x8e\x01\n\nDepartment\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x14\n\x0cmember_count\x18\x04 \x01(\x05\x12\x17\n\nvisibility\x18\x05 \x01(\tH\x00\x88\x01\x01\x12\x13\n\x0b\x61gent_count\x18\x06 \x01(\x05\x42\r\n\x0b_visibility\"\x83\x01\n\x1bOrganisationAndDeptResponse\x12\x35\n\x0corganisation\x18\x01 \x01(\x0b\x32\x1f.organisation.OrganisationModel\x12-\n\x0b\x64\x65partments\x18\x02 \x03(\x0b\x32\x18.organisation.Department\"y\n\x14OrganisationResponse\x12?\n\x0corganisation\x18\x01 \x01(\x0b\x32).organisation.OrganisationAndDeptResponse\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\"\x83\x01\n\x18ListOrganisationsRequest\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\x13\n\x0bsearch_term\x18\x03 \x01(\t\x12\x1a\n\x12\x66ilter_by_industry\x18\x04 \x01(\t\x12\x15\n\rfilter_by_tag\x18\x05 \x01(\t\"\x89\x01\n\x19ListOrganisationsResponse\x12\x36\n\rorganisations\x18\x01 \x03(\x0b\x32\x1f.organisation.OrganisationModel\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\xaf\x01\n\x17\x43reateDepartmentRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x1c\n\x14parent_department_id\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x05 \x01(\t\x12\x17\n\nvisibility\x18\x06 \x01(\tH\x00\x88\x01\x01\x42\r\n\x0b_visibility\"\"\n\x14GetDepartmentRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\x8f\x01\n\x16ListDepartmentsRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x13\n\x0bsearch_term\x18\x04 \x01(\t\x12\x15\n\rdepartment_id\x18\x05 \x01(\t\x12\x0f\n\x07user_id\x18\x06 \x01(\t\"\x86\x02\n\x0f\x44\x65partmentModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x1c\n\x14parent_department_id\x18\x05 \x01(\t\x12\x12\n\ncreated_by\x18\x06 \x01(\t\x12\x12\n\ncreated_at\x18\x07 \x01(\t\x12\x12\n\nupdated_at\x18\x08 \x01(\t\x12\x14\n\x0cmember_count\x18\t \x01(\x03\x12\x13\n\x0b\x61gent_count\x18\n \x01(\x03\x12\x17\n\nvisibility\x18\x0b \x01(\tH\x00\x88\x01\x01\x42\r\n\x0b_visibility\"X\n\x12\x44\x65partmentResponse\x12\x31\n\ndepartment\x18\x01 \x01(\x0b\x32\x1d.organisation.DepartmentModel\x12\x0f\n\x07message\x18\x02 \x01(\t\"\x83\x01\n\x17ListDepartmentsResponse\x12\x32\n\x0b\x64\x65partments\x18\x01 \x03(\x0b\x32\x1d.organisation.DepartmentModel\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\x85\x01\n\x11InviteUserRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x0c\n\x04role\x18\x03 \x01(\t\x12\x12\n\ndepartment\x18\x04 \x01(\t\x12\x12\n\npermission\x18\x05 \x01(\t\x12\x12\n\ncreated_by\x18\x06 \x01(\t\"\x1e\n\x10GetInviteRequest\x12\n\n\x02id\x18\x01 \x01(\t\"^\n\x12ListInvitesRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x0e\n\x06status\x18\x04 \x01(\t\"\xd7\x01\n\x0bInviteModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x17\n\x0forganisation_id\x18\x03 \x01(\t\x12\x12\n\ndepartment\x18\x04 \x01(\t\x12\x0c\n\x04role\x18\x05 \x01(\t\x12\x12\n\npermission\x18\x06 \x01(\t\x12\x12\n\ncreated_by\x18\x07 \x01(\t\x12\x0e\n\x06status\x18\x08 \x01(\t\x12\x12\n\ncreated_at\x18\t \x01(\t\x12\x12\n\nupdated_at\x18\n \x01(\t\x12\x12\n\nexpires_at\x18\x0b \x01(\t\"]\n\x0eInviteResponse\x12)\n\x06invite\x18\x01 \x01(\x0b\x32\x19.organisation.InviteModel\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\"w\n\x13ListInvitesResponse\x12*\n\x07invites\x18\x01 \x03(\x0b\x32\x19.organisation.InviteModel\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\x81\x01\n\x19\x41\x63\x63\x65ptInviteByLinkRequest\x12\x14\n\x0cinvite_token\x18\x01 \x01(\t\x12\x1a\n\x12\x63urrent_user_email\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x11\n\tuser_name\x18\x04 \x01(\t\x12\x0e\n\x06\x61\x63\x63\x65pt\x18\x05 \x01(\x08\"l\n\x1cGrantDepartmentAccessRequest\x12\x15\n\rdepartment_id\x18\x01 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x02 \x03(\t\x12\x12\n\nfolder_ids\x18\x03 \x03(\t\x12\x0f\n\x07user_id\x18\x04 \x01(\t\"A\n\x1dGrantDepartmentAccessResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"S\n\x14\x44\x65partmentAccessData\x12\x15\n\rdepartment_id\x18\x01 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x02 \x03(\t\x12\x12\n\nfolder_ids\x18\x03 \x03(\t\"q\n!BatchGrantDepartmentAccessRequest\x12;\n\x0f\x64\x65partment_data\x18\x01 \x03(\x0b\x32\".organisation.DepartmentAccessData\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"e\n\"BatchGrantDepartmentAccessResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x1d\n\x15\x66\x61iled_department_ids\x18\x03 \x03(\t\"-\n\x1aListTopLevelFoldersRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\"\"\n\x06\x46older\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"f\n\x1bListTopLevelFoldersResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12%\n\x07\x66olders\x18\x03 \x03(\x0b\x32\x14.organisation.Folder\"\x90\x01\n\x0bSourceModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12&\n\x04type\x18\x03 \x01(\x0e\x32\x18.organisation.SourceType\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x12\n\ncreated_at\x18\x05 \x01(\t\x12\x12\n\nupdated_at\x18\x06 \x01(\t\"\xe1\x01\n\x10\x41\x64\x64SourceRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12&\n\x04type\x18\x02 \x01(\x0e\x32\x18.organisation.SourceType\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x1d\n\x10\x63redentials_file\x18\x04 \x01(\tH\x00\x88\x01\x01\x12 \n\x13service_account_key\x18\x05 \x01(\tH\x01\x88\x01\x01\x12\x10\n\x08\x66ile_ids\x18\x06 \x03(\tB\x13\n\x11_credentials_fileB\x16\n\x14_service_account_key\"$\n\x08\x46ileInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"\x8e\x01\n\x11\x41\x64\x64SourceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x06source\x18\x03 \x01(\x0b\x32\x19.organisation.SourceModel\x12,\n\x0csynced_files\x18\x04 \x03(\x0b\x32\x16.organisation.FileInfo\"-\n\x12ListSourcesRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\"c\n\x13ListSourcesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12*\n\x07sources\x18\x03 \x03(\x0b\x32\x19.organisation.SourceModel\"9\n\x13\x44\x65leteSourceRequest\x12\x11\n\tsource_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"8\n\x14\x44\x65leteSourceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\xb2\x01\n\x1eUpdateSourceCredentialsRequest\x12\x11\n\tsource_id\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x1d\n\x10\x63redentials_file\x18\x03 \x01(\tH\x00\x88\x01\x01\x12 \n\x13service_account_key\x18\x04 \x01(\tH\x01\x88\x01\x01\x42\x13\n\x11_credentials_fileB\x16\n\x14_service_account_key\"n\n\x1fUpdateSourceCredentialsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x06source\x18\x03 \x01(\x0b\x32\x19.organisation.SourceModel\"C\n\x15ValidateSourceRequest\x12\x11\n\tsource_id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\"\x85\x01\n\x16ValidateSourceResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x30\n\x12\x61\x63\x63\x65ssible_folders\x18\x03 \x03(\x0b\x32\x14.organisation.Folder\x12\x17\n\x0f\x63redential_type\x18\x04 \x01(\t\".\n\x1bGetUserOrganisationsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\"o\n\x10UserOrganisation\x12\x35\n\x0corganisation\x18\x01 \x01(\x0b\x32\x1f.organisation.OrganisationModel\x12\x12\n\nis_primary\x18\x02 \x01(\x08\x12\x10\n\x08is_admin\x18\x03 \x01(\x08\"\xe1\x01\n\rPendingInvite\x12\x11\n\tinvite_id\x18\x01 \x01(\t\x12\x35\n\x0corganisation\x18\x02 \x01(\x0b\x32\x1f.organisation.OrganisationModel\x12\x12\n\ndepartment\x18\x03 \x01(\t\x12\x0c\n\x04role\x18\x04 \x01(\t\x12\x12\n\npermission\x18\x05 \x01(\t\x12\x12\n\ninviter_id\x18\x06 \x01(\t\x12\x14\n\x0cinviter_name\x18\x07 \x01(\t\x12\x12\n\ncreated_at\x18\x08 \x01(\t\x12\x12\n\nexpires_at\x18\t \x01(\t\"\xd6\x01\n\x19UserOrganisationsResponse\x12\x35\n\rorganisations\x18\x01 \x03(\x0b\x32\x1e.organisation.UserOrganisation\x12\x34\n\x0fpending_invites\x18\x02 \x03(\x0b\x32\x1b.organisation.PendingInvite\x12\x16\n\x0epersonal_space\x18\x03 \x01(\x08\x12\x12\n\nhas_joined\x18\x04 \x01(\x08\x12\x0f\n\x07message\x18\x05 \x01(\t\x12\x0f\n\x07success\x18\x06 \x01(\x08\"S\n\x19ListInviterInvitesRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x17\n\x0forganisation_id\x18\x03 \x01(\t\"\x85\x02\n\rInviterInvite\x12\x12\n\ninvitee_id\x18\x01 \x01(\t\x12\x14\n\x0cinvitee_name\x18\x02 \x01(\t\x12\x15\n\rinvitee_email\x18\x03 \x01(\t\x12\x17\n\x0forganisation_id\x18\x04 \x01(\t\x12\x19\n\x11organisation_name\x18\x05 \x01(\t\x12\x12\n\ndepartment\x18\x06 \x01(\t\x12\x0c\n\x04role\x18\x07 \x01(\t\x12\x12\n\npermission\x18\x08 \x01(\t\x12\x0e\n\x06status\x18\t \x01(\t\x12\x11\n\tjoined_at\x18\n \x01(\t\x12\x12\n\ncreated_at\x18\x0b \x01(\t\x12\x12\n\nexpires_at\x18\x0c \x01(\t\"l\n\x1aListInviterInvitesResponse\x12,\n\x07invites\x18\x01 \x03(\x0b\x32\x1b.organisation.InviterInvite\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\"[\n\x0e\x44\x65partmentUser\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\x12\x0c\n\x04role\x18\x04 \x01(\t\x12\x12\n\npermission\x18\x05 \x01(\t\"l\n\x19GetDepartmentUsersRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x15\n\rdepartment_id\x18\x02 \x01(\t\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\xd3\x01\n\x1aGetDepartmentUsersResponse\x12+\n\x05users\x18\x01 \x03(\x0b\x32\x1c.organisation.DepartmentUser\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\x12\x0f\n\x07message\x18\x05 \x01(\t\x12\x0f\n\x07success\x18\x06 \x01(\x08\x12\x17\n\x0f\x64\x65partment_name\x18\x07 \x01(\t\x12\x17\n\x0f\x64\x65partment_desc\x18\x08 \x01(\t*%\n\nVisibility\x12\x0b\n\x07PRIVATE\x10\x00\x12\n\n\x06PUBLIC\x10\x01*)\n\nSourceType\x12\x10\n\x0cGOOGLE_DRIVE\x10\x00\x12\t\n\x05SLACK\x10\x01*D\n\x0cInviteStatus\x12\x0b\n\x07PENDING\x10\x00\x12\x0c\n\x08\x41\x43\x43\x45PTED\x10\x01\x12\x0c\n\x08\x44\x45\x43LINED\x10\x02\x12\x0b\n\x07\x45XPIRED\x10\x03\x32\xc9\r\n\x13OrganisationService\x12\x63\n\x12\x63reateOrganisation\x12\'.organisation.CreateOrganisationRequest\x1a\".organisation.OrganisationResponse\"\x00\x12]\n\x0fgetOrganisation\x12$.organisation.GetOrganisationRequest\x1a\".organisation.OrganisationResponse\"\x00\x12]\n\x10\x63reateDepartment\x12%.organisation.CreateDepartmentRequest\x1a .organisation.DepartmentResponse\"\x00\x12`\n\x0flistDepartments\x12$.organisation.ListDepartmentsRequest\x1a%.organisation.ListDepartmentsResponse\"\x00\x12M\n\ninviteUser\x12\x1f.organisation.InviteUserRequest\x1a\x1c.organisation.InviteResponse\"\x00\x12]\n\x12\x61\x63\x63\x65ptInviteByLink\x12\'.organisation.AcceptInviteByLinkRequest\x1a\x1c.organisation.InviteResponse\"\x00\x12l\n\x14getUserOrganisations\x12).organisation.GetUserOrganisationsRequest\x1a\'.organisation.UserOrganisationsResponse\"\x00\x12r\n\x15grantDepartmentAccess\x12*.organisation.GrantDepartmentAccessRequest\x1a+.organisation.GrantDepartmentAccessResponse\"\x00\x12\x81\x01\n\x1a\x62\x61tchGrantDepartmentAccess\x12/.organisation.BatchGrantDepartmentAccessRequest\x1a\x30.organisation.BatchGrantDepartmentAccessResponse\"\x00\x12l\n\x13listTopLevelFolders\x12(.organisation.ListTopLevelFoldersRequest\x1a).organisation.ListTopLevelFoldersResponse\"\x00\x12N\n\taddSource\x12\x1e.organisation.AddSourceRequest\x1a\x1f.organisation.AddSourceResponse\"\x00\x12T\n\x0blistSources\x12 .organisation.ListSourcesRequest\x1a!.organisation.ListSourcesResponse\"\x00\x12W\n\x0c\x64\x65leteSource\x12!.organisation.DeleteSourceRequest\x1a\".organisation.DeleteSourceResponse\"\x00\x12x\n\x17updateSourceCredentials\x12,.organisation.UpdateSourceCredentialsRequest\x1a-.organisation.UpdateSourceCredentialsResponse\"\x00\x12]\n\x0evalidateSource\x12#.organisation.ValidateSourceRequest\x1a$.organisation.ValidateSourceResponse\"\x00\x12h\n\x11getInviterInvites\x12\'.organisation.ListInviterInvitesRequest\x1a(.organisation.ListInviterInvitesResponse\"\x00\x12i\n\x12getDepartmentUsers\x12\'.organisation.GetDepartmentUsersRequest\x1a(.organisation.GetDepartmentUsersResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'organisation_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_VISIBILITY']._serialized_start=6190
  _globals['_VISIBILITY']._serialized_end=6227
  _globals['_SOURCETYPE']._serialized_start=6229
  _globals['_SOURCETYPE']._serialized_end=6270
  _globals['_INVITESTATUS']._serialized_start=6272
  _globals['_INVITESTATUS']._serialized_end=6340
  _globals['_CREATEORGANISATIONREQUEST']._serialized_start=37
  _globals['_CREATEORGANISATIONREQUEST']._serialized_end=178
  _globals['_GETORGANISATIONREQUEST']._serialized_start=180
  _globals['_GETORGANISATIONREQUEST']._serialized_end=216
  _globals['_ORGANISATIONMODEL']._serialized_start=219
  _globals['_ORGANISATIONMODEL']._serialized_end=363
  _globals['_DEPARTMENT']._serialized_start=366
  _globals['_DEPARTMENT']._serialized_end=508
  _globals['_ORGANISATIONANDDEPTRESPONSE']._serialized_start=511
  _globals['_ORGANISATIONANDDEPTRESPONSE']._serialized_end=642
  _globals['_ORGANISATIONRESPONSE']._serialized_start=644
  _globals['_ORGANISATIONRESPONSE']._serialized_end=765
  _globals['_LISTORGANISATIONSREQUEST']._serialized_start=768
  _globals['_LISTORGANISATIONSREQUEST']._serialized_end=899
  _globals['_LISTORGANISATIONSRESPONSE']._serialized_start=902
  _globals['_LISTORGANISATIONSRESPONSE']._serialized_end=1039
  _globals['_CREATEDEPARTMENTREQUEST']._serialized_start=1042
  _globals['_CREATEDEPARTMENTREQUEST']._serialized_end=1217
  _globals['_GETDEPARTMENTREQUEST']._serialized_start=1219
  _globals['_GETDEPARTMENTREQUEST']._serialized_end=1253
  _globals['_LISTDEPARTMENTSREQUEST']._serialized_start=1256
  _globals['_LISTDEPARTMENTSREQUEST']._serialized_end=1399
  _globals['_DEPARTMENTMODEL']._serialized_start=1402
  _globals['_DEPARTMENTMODEL']._serialized_end=1664
  _globals['_DEPARTMENTRESPONSE']._serialized_start=1666
  _globals['_DEPARTMENTRESPONSE']._serialized_end=1754
  _globals['_LISTDEPARTMENTSRESPONSE']._serialized_start=1757
  _globals['_LISTDEPARTMENTSRESPONSE']._serialized_end=1888
  _globals['_INVITEUSERREQUEST']._serialized_start=1891
  _globals['_INVITEUSERREQUEST']._serialized_end=2024
  _globals['_GETINVITEREQUEST']._serialized_start=2026
  _globals['_GETINVITEREQUEST']._serialized_end=2056
  _globals['_LISTINVITESREQUEST']._serialized_start=2058
  _globals['_LISTINVITESREQUEST']._serialized_end=2152
  _globals['_INVITEMODEL']._serialized_start=2155
  _globals['_INVITEMODEL']._serialized_end=2370
  _globals['_INVITERESPONSE']._serialized_start=2372
  _globals['_INVITERESPONSE']._serialized_end=2465
  _globals['_LISTINVITESRESPONSE']._serialized_start=2467
  _globals['_LISTINVITESRESPONSE']._serialized_end=2586
  _globals['_ACCEPTINVITEBYLINKREQUEST']._serialized_start=2589
  _globals['_ACCEPTINVITEBYLINKREQUEST']._serialized_end=2718
  _globals['_GRANTDEPARTMENTACCESSREQUEST']._serialized_start=2720
  _globals['_GRANTDEPARTMENTACCESSREQUEST']._serialized_end=2828
  _globals['_GRANTDEPARTMENTACCESSRESPONSE']._serialized_start=2830
  _globals['_GRANTDEPARTMENTACCESSRESPONSE']._serialized_end=2895
  _globals['_DEPARTMENTACCESSDATA']._serialized_start=2897
  _globals['_DEPARTMENTACCESSDATA']._serialized_end=2980
  _globals['_BATCHGRANTDEPARTMENTACCESSREQUEST']._serialized_start=2982
  _globals['_BATCHGRANTDEPARTMENTACCESSREQUEST']._serialized_end=3095
  _globals['_BATCHGRANTDEPARTMENTACCESSRESPONSE']._serialized_start=3097
  _globals['_BATCHGRANTDEPARTMENTACCESSRESPONSE']._serialized_end=3198
  _globals['_LISTTOPLEVELFOLDERSREQUEST']._serialized_start=3200
  _globals['_LISTTOPLEVELFOLDERSREQUEST']._serialized_end=3245
  _globals['_FOLDER']._serialized_start=3247
  _globals['_FOLDER']._serialized_end=3281
  _globals['_LISTTOPLEVELFOLDERSRESPONSE']._serialized_start=3283
  _globals['_LISTTOPLEVELFOLDERSRESPONSE']._serialized_end=3385
  _globals['_SOURCEMODEL']._serialized_start=3388
  _globals['_SOURCEMODEL']._serialized_end=3532
  _globals['_ADDSOURCEREQUEST']._serialized_start=3535
  _globals['_ADDSOURCEREQUEST']._serialized_end=3760
  _globals['_FILEINFO']._serialized_start=3762
  _globals['_FILEINFO']._serialized_end=3798
  _globals['_ADDSOURCERESPONSE']._serialized_start=3801
  _globals['_ADDSOURCERESPONSE']._serialized_end=3943
  _globals['_LISTSOURCESREQUEST']._serialized_start=3945
  _globals['_LISTSOURCESREQUEST']._serialized_end=3990
  _globals['_LISTSOURCESRESPONSE']._serialized_start=3992
  _globals['_LISTSOURCESRESPONSE']._serialized_end=4091
  _globals['_DELETESOURCEREQUEST']._serialized_start=4093
  _globals['_DELETESOURCEREQUEST']._serialized_end=4150
  _globals['_DELETESOURCERESPONSE']._serialized_start=4152
  _globals['_DELETESOURCERESPONSE']._serialized_end=4208
  _globals['_UPDATESOURCECREDENTIALSREQUEST']._serialized_start=4211
  _globals['_UPDATESOURCECREDENTIALSREQUEST']._serialized_end=4389
  _globals['_UPDATESOURCECREDENTIALSRESPONSE']._serialized_start=4391
  _globals['_UPDATESOURCECREDENTIALSRESPONSE']._serialized_end=4501
  _globals['_VALIDATESOURCEREQUEST']._serialized_start=4503
  _globals['_VALIDATESOURCEREQUEST']._serialized_end=4570
  _globals['_VALIDATESOURCERESPONSE']._serialized_start=4573
  _globals['_VALIDATESOURCERESPONSE']._serialized_end=4706
  _globals['_GETUSERORGANISATIONSREQUEST']._serialized_start=4708
  _globals['_GETUSERORGANISATIONSREQUEST']._serialized_end=4754
  _globals['_USERORGANISATION']._serialized_start=4756
  _globals['_USERORGANISATION']._serialized_end=4867
  _globals['_PENDINGINVITE']._serialized_start=4870
  _globals['_PENDINGINVITE']._serialized_end=5095
  _globals['_USERORGANISATIONSRESPONSE']._serialized_start=5098
  _globals['_USERORGANISATIONSRESPONSE']._serialized_end=5312
  _globals['_LISTINVITERINVITESREQUEST']._serialized_start=5314
  _globals['_LISTINVITERINVITESREQUEST']._serialized_end=5397
  _globals['_INVITERINVITE']._serialized_start=5400
  _globals['_INVITERINVITE']._serialized_end=5661
  _globals['_LISTINVITERINVITESRESPONSE']._serialized_start=5663
  _globals['_LISTINVITERINVITESRESPONSE']._serialized_end=5771
  _globals['_DEPARTMENTUSER']._serialized_start=5773
  _globals['_DEPARTMENTUSER']._serialized_end=5864
  _globals['_GETDEPARTMENTUSERSREQUEST']._serialized_start=5866
  _globals['_GETDEPARTMENTUSERSREQUEST']._serialized_end=5974
  _globals['_GETDEPARTMENTUSERSRESPONSE']._serialized_start=5977
  _globals['_GETDEPARTMENTUSERSRESPONSE']._serialized_end=6188
  _globals['_ORGANISATIONSERVICE']._serialized_start=6343
  _globals['_ORGANISATIONSERVICE']._serialized_end=8080
# @@protoc_insertion_point(module_scope)
