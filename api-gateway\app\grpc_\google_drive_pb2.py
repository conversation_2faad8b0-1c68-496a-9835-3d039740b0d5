# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: google_drive.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'google_drive.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12google_drive.proto\x12\x0cgoogle_drive\"=\n\x11GetAuthUrlRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\"W\n\x12GetAuthUrlResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x10\n\x08\x61uth_url\x18\x03 \x01(\t\x12\r\n\x05state\x18\x04 \x01(\t\"7\n\x13\x43onnectDriveRequest\x12\x11\n\tauth_code\x18\x01 \x01(\t\x12\r\n\x05state\x18\x02 \x01(\t\"c\n\x14\x43onnectDriveResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x07\x66olders\x18\x03 \x03(\x0b\x32\x18.google_drive.FolderInfo\"&\n\nFolderInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\")\n\x16\x44isconnectDriveRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\";\n\x17\x44isconnectDriveResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"O\n\x10SyncDriveRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x17\n\x0forganisation_id\x18\x02 \x01(\t\x12\x11\n\tfull_sync\x18\x03 \x01(\x08\"x\n\x11SyncDriveResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x14\n\x0c\x66iles_synced\x18\x03 \x01(\x05\x12\x16\n\x0e\x66olders_synced\x18\x04 \x01(\x05\x12\x13\n\x0bsync_status\x18\x05 \x01(\t\"W\n\x10ListFilesRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x11\n\tfolder_id\x18\x02 \x01(\t\x12\x0c\n\x04page\x18\x03 \x01(\x05\x12\x11\n\tpage_size\x18\x04 \x01(\x05\"\xe6\x01\n\x0e\x44riveFileModel\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tmime_type\x18\x03 \x01(\t\x12\x15\n\rweb_view_link\x18\x04 \x01(\t\x12\x14\n\x0c\x63reated_time\x18\x05 \x01(\t\x12\x15\n\rmodified_time\x18\x06 \x01(\t\x12\x18\n\x10parent_folder_id\x18\x07 \x01(\t\x12\x0c\n\x04size\x18\x08 \x01(\x03\x12\x13\n\x0bshared_with\x18\t \x03(\t\x12\x11\n\tis_folder\x18\n \x01(\x08\x12\x13\n\x0b\x63hild_count\x18\x0b \x01(\x05\"\x98\x01\n\x11ListFilesResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12+\n\x05\x66iles\x18\x03 \x03(\x0b\x32\x1c.google_drive.DriveFileModel\x12\x13\n\x0btotal_count\x18\x04 \x01(\x05\x12\x0c\n\x04page\x18\x05 \x01(\x05\x12\x11\n\tpage_size\x18\x06 \x01(\x05\"9\n\x15GetFileDetailsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0f\n\x07\x66ile_id\x18\x02 \x01(\t\"f\n\x16GetFileDetailsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12*\n\x04\x66ile\x18\x03 \x01(\x0b\x32\x1c.google_drive.DriveFileModel\"B\n\x14GetFolderByIdRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x11\n\tfolder_id\x18\x02 \x01(\t\"\x97\x01\n\x15GetFolderByIdResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12,\n\x06\x66older\x18\x03 \x01(\x0b\x32\x1c.google_drive.DriveFileModel\x12.\n\x08\x63hildren\x18\x04 \x03(\x0b\x32\x1c.google_drive.DriveFileModel\"E\n\x16SyncFolderByIdsRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\x12\x12\n\nfolder_ids\x18\x02 \x03(\t\"\xb0\x01\n\x17SyncFolderByIdsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x14\n\x0c\x66iles_synced\x18\x03 \x01(\x05\x12\x16\n\x0e\x66olders_synced\x18\x04 \x01(\x05\x12\x13\n\x0bsync_status\x18\x05 \x01(\t\x12\x30\n\x0esynced_folders\x18\x06 \x03(\x0b\x32\x18.google_drive.FolderInfo\":\n\x16\x43heckFileAccessRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x0f\n\x07\x66ile_id\x18\x02 \x01(\t\"O\n\x17\x43heckFileAccessResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\nhas_access\x18\x03 \x01(\x08\"\x90\x01\n\x1dSearchSimilarDocumentsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x12\n\nquery_text\x18\x02 \x01(\t\x12\r\n\x05top_k\x18\x03 \x01(\x05\x12\x10\n\x08\x61gent_id\x18\x04 \x01(\t\x12\x17\n\x0forganisation_id\x18\x05 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x06 \x03(\t\"\xc3\x01\n\x10SearchResultItem\x12\x0f\n\x07\x66ile_id\x18\x01 \x01(\t\x12\x11\n\tfile_name\x18\x02 \x01(\t\x12\x11\n\tmime_type\x18\x03 \x01(\t\x12\x15\n\rweb_view_link\x18\x04 \x01(\t\x12\x14\n\x0c\x63reated_time\x18\x05 \x01(\t\x12\x15\n\rmodified_time\x18\x06 \x01(\t\x12\r\n\x05score\x18\x07 \x01(\x02\x12\x11\n\tvector_id\x18\x08 \x01(\t\x12\x12\n\nchunk_text\x18\t \x01(\t\"s\n\x1eSearchSimilarDocumentsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12/\n\x07results\x18\x03 \x03(\x0b\x32\x1e.google_drive.SearchResultItem\"\x96\x01\n\"BatchSearchSimilarDocumentsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x13\n\x0bquery_texts\x18\x02 \x03(\t\x12\r\n\x05top_k\x18\x03 \x01(\x05\x12\x10\n\x08\x61gent_id\x18\x04 \x01(\t\x12\x17\n\x0forganisation_id\x18\x05 \x01(\t\x12\x10\n\x08\x66ile_ids\x18\x06 \x03(\t\"z\n#BatchSearchSimilarDocumentsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x31\n\rquery_results\x18\x03 \x03(\x0b\x32\x1a.google_drive.QueryResults\"S\n\x0cQueryResults\x12\x12\n\nquery_text\x18\x01 \x01(\t\x12/\n\x07results\x18\x02 \x03(\x0b\x32\x1e.google_drive.SearchResultItem\"e\n\x14SyncFileByUrlRequest\x12\x11\n\tdrive_url\x18\x01 \x01(\t\x12\x10\n\x08\x61gent_id\x18\x02 \x01(\t\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x17\n\x0forganisation_id\x18\x04 \x01(\t\"r\n\x15SyncFileByUrlResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07\x66ile_id\x18\x03 \x01(\t\x12\x11\n\tfile_name\x18\x04 \x01(\t\x12\x13\n\x0bsync_status\x18\x05 \x01(\t\"B\n\'GetServiceAccountTopLevelFoldersRequest\x12\x17\n\x0forganisation_id\x18\x01 \x01(\t\"w\n(GetServiceAccountTopLevelFoldersResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12)\n\x07\x66olders\x18\x03 \x03(\x0b\x32\x18.google_drive.FolderInfo2\xb1\n\n\x12GoogleDriveService\x12Q\n\ngetAuthUrl\x12\x1f.google_drive.GetAuthUrlRequest\x1a .google_drive.GetAuthUrlResponse\"\x00\x12W\n\x0c\x63onnectDrive\x12!.google_drive.ConnectDriveRequest\x1a\".google_drive.ConnectDriveResponse\"\x00\x12`\n\x0f\x64isconnectDrive\x12$.google_drive.DisconnectDriveRequest\x1a%.google_drive.DisconnectDriveResponse\"\x00\x12N\n\tsyncDrive\x12\x1e.google_drive.SyncDriveRequest\x1a\x1f.google_drive.SyncDriveResponse\"\x00\x12N\n\tlistFiles\x12\x1e.google_drive.ListFilesRequest\x1a\x1f.google_drive.ListFilesResponse\"\x00\x12]\n\x0egetFileDetails\x12#.google_drive.GetFileDetailsRequest\x1a$.google_drive.GetFileDetailsResponse\"\x00\x12Z\n\rgetFolderById\x12\".google_drive.GetFolderByIdRequest\x1a#.google_drive.GetFolderByIdResponse\"\x00\x12`\n\x0fsyncFolderByIds\x12$.google_drive.SyncFolderByIdsRequest\x1a%.google_drive.SyncFolderByIdsResponse\"\x00\x12`\n\x0f\x63heckFileAccess\x12$.google_drive.CheckFileAccessRequest\x1a%.google_drive.CheckFileAccessResponse\"\x00\x12u\n\x16searchSimilarDocuments\x12+.google_drive.SearchSimilarDocumentsRequest\x1a,.google_drive.SearchSimilarDocumentsResponse\"\x00\x12\x84\x01\n\x1b\x62\x61tchSearchSimilarDocuments\x12\x30.google_drive.BatchSearchSimilarDocumentsRequest\x1a\x31.google_drive.BatchSearchSimilarDocumentsResponse\"\x00\x12Z\n\rsyncFileByUrl\x12\".google_drive.SyncFileByUrlRequest\x1a#.google_drive.SyncFileByUrlResponse\"\x00\x12\x93\x01\n getServiceAccountTopLevelFolders\x12\x35.google_drive.GetServiceAccountTopLevelFoldersRequest\x1a\x36.google_drive.GetServiceAccountTopLevelFoldersResponse\"\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'google_drive_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_GETAUTHURLREQUEST']._serialized_start=36
  _globals['_GETAUTHURLREQUEST']._serialized_end=97
  _globals['_GETAUTHURLRESPONSE']._serialized_start=99
  _globals['_GETAUTHURLRESPONSE']._serialized_end=186
  _globals['_CONNECTDRIVEREQUEST']._serialized_start=188
  _globals['_CONNECTDRIVEREQUEST']._serialized_end=243
  _globals['_CONNECTDRIVERESPONSE']._serialized_start=245
  _globals['_CONNECTDRIVERESPONSE']._serialized_end=344
  _globals['_FOLDERINFO']._serialized_start=346
  _globals['_FOLDERINFO']._serialized_end=384
  _globals['_DISCONNECTDRIVEREQUEST']._serialized_start=386
  _globals['_DISCONNECTDRIVEREQUEST']._serialized_end=427
  _globals['_DISCONNECTDRIVERESPONSE']._serialized_start=429
  _globals['_DISCONNECTDRIVERESPONSE']._serialized_end=488
  _globals['_SYNCDRIVEREQUEST']._serialized_start=490
  _globals['_SYNCDRIVEREQUEST']._serialized_end=569
  _globals['_SYNCDRIVERESPONSE']._serialized_start=571
  _globals['_SYNCDRIVERESPONSE']._serialized_end=691
  _globals['_LISTFILESREQUEST']._serialized_start=693
  _globals['_LISTFILESREQUEST']._serialized_end=780
  _globals['_DRIVEFILEMODEL']._serialized_start=783
  _globals['_DRIVEFILEMODEL']._serialized_end=1013
  _globals['_LISTFILESRESPONSE']._serialized_start=1016
  _globals['_LISTFILESRESPONSE']._serialized_end=1168
  _globals['_GETFILEDETAILSREQUEST']._serialized_start=1170
  _globals['_GETFILEDETAILSREQUEST']._serialized_end=1227
  _globals['_GETFILEDETAILSRESPONSE']._serialized_start=1229
  _globals['_GETFILEDETAILSRESPONSE']._serialized_end=1331
  _globals['_GETFOLDERBYIDREQUEST']._serialized_start=1333
  _globals['_GETFOLDERBYIDREQUEST']._serialized_end=1399
  _globals['_GETFOLDERBYIDRESPONSE']._serialized_start=1402
  _globals['_GETFOLDERBYIDRESPONSE']._serialized_end=1553
  _globals['_SYNCFOLDERBYIDSREQUEST']._serialized_start=1555
  _globals['_SYNCFOLDERBYIDSREQUEST']._serialized_end=1624
  _globals['_SYNCFOLDERBYIDSRESPONSE']._serialized_start=1627
  _globals['_SYNCFOLDERBYIDSRESPONSE']._serialized_end=1803
  _globals['_CHECKFILEACCESSREQUEST']._serialized_start=1805
  _globals['_CHECKFILEACCESSREQUEST']._serialized_end=1863
  _globals['_CHECKFILEACCESSRESPONSE']._serialized_start=1865
  _globals['_CHECKFILEACCESSRESPONSE']._serialized_end=1944
  _globals['_SEARCHSIMILARDOCUMENTSREQUEST']._serialized_start=1947
  _globals['_SEARCHSIMILARDOCUMENTSREQUEST']._serialized_end=2091
  _globals['_SEARCHRESULTITEM']._serialized_start=2094
  _globals['_SEARCHRESULTITEM']._serialized_end=2289
  _globals['_SEARCHSIMILARDOCUMENTSRESPONSE']._serialized_start=2291
  _globals['_SEARCHSIMILARDOCUMENTSRESPONSE']._serialized_end=2406
  _globals['_BATCHSEARCHSIMILARDOCUMENTSREQUEST']._serialized_start=2409
  _globals['_BATCHSEARCHSIMILARDOCUMENTSREQUEST']._serialized_end=2559
  _globals['_BATCHSEARCHSIMILARDOCUMENTSRESPONSE']._serialized_start=2561
  _globals['_BATCHSEARCHSIMILARDOCUMENTSRESPONSE']._serialized_end=2683
  _globals['_QUERYRESULTS']._serialized_start=2685
  _globals['_QUERYRESULTS']._serialized_end=2768
  _globals['_SYNCFILEBYURLREQUEST']._serialized_start=2770
  _globals['_SYNCFILEBYURLREQUEST']._serialized_end=2871
  _globals['_SYNCFILEBYURLRESPONSE']._serialized_start=2873
  _globals['_SYNCFILEBYURLRESPONSE']._serialized_end=2987
  _globals['_GETSERVICEACCOUNTTOPLEVELFOLDERSREQUEST']._serialized_start=2989
  _globals['_GETSERVICEACCOUNTTOPLEVELFOLDERSREQUEST']._serialized_end=3055
  _globals['_GETSERVICEACCOUNTTOPLEVELFOLDERSRESPONSE']._serialized_start=3057
  _globals['_GETSERVICEACCOUNTTOPLEVELFOLDERSRESPONSE']._serialized_end=3176
  _globals['_GOOGLEDRIVESERVICE']._serialized_start=3179
  _globals['_GOOGLEDRIVESERVICE']._serialized_end=4508
# @@protoc_insertion_point(module_scope)
