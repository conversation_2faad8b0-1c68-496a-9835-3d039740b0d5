from datetime import datetime
import uuid
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Integer,
    Float,
    ForeignKey,
    Enum,
    JSON,
    Boolean,
    ARRAY,
)

from sqlalchemy.orm import declarative_base
from app.utils.constants.constants import (
    WorkflowCategoryEnum,
    WorkflowOwnerTypeEnum,
    WorkflowVisibilityEnum,
    WorkflowStatusEnum,
)
from app.utils.constants.table_names import (
    WORKFLOW_TABLE,
    WORKFLOW_TEMPLATE_TABLE,
)

Base = declarative_base()


class WorkflowTemplate(Base):
    __tablename__ = WORKFLOW_TEMPLATE_TABLE

    # Primary Fields
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(String, nullable=True)

    image_url = Column(String, nullable=True)

    workflow_url = Column(String, nullable=False)  # Stores the workflow schema

    builder_url = Column(String, nullable=False)  # Stores the builder schema

    start_nodes = Column(JSON, nullable=False, default=list)  # Add this line
    available_nodes = Column(JSON, nullable=False, default=list)

    # Owner reference - add this
    owner_id = Column(String, nullable=False)

    # Rename to match agent template naming
    use_count = Column(Integer, default=0)  # Renamed from execution_count

    execution_count = Column(Integer, default=0)

    average_rating = Column(Float, default=0)

    category = Column(Enum(WorkflowCategoryEnum), nullable=True)

    tags = Column(JSON, nullable=True)

    version = Column(String(50), nullable=False, default="1.0.0")

    status = Column(Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE)

    visibility = Column(
        Enum(WorkflowVisibilityEnum), nullable=True, default=WorkflowVisibilityEnum.PUBLIC
    )

    is_customizable = Column(Boolean, default=False)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<WorkflowTemplate {self.name}>"


class Workflow(Base):
    __tablename__ = WORKFLOW_TABLE

    # Primary Fields
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(String, nullable=True)

    image_url = Column(String, nullable=True)

    workflow_url = Column(String, nullable=False)
    builder_url = Column(String, nullable=False)
    start_nodes = Column(JSON, nullable=False, default=list)
    available_nodes = Column(JSON, nullable=False, default=list)

    # Access Control
    owner_id = Column(String, nullable=False)
    user_ids = Column(ARRAY(String), nullable=True)
    owner_type = Column(Enum(WorkflowOwnerTypeEnum), nullable=False)

    # Template Reference
    workflow_template_id = Column(
        String, ForeignKey(f"{WORKFLOW_TEMPLATE_TABLE}.id"), nullable=True
    )
    template_owner_id = Column(String, nullable=True)
    url = Column(String, nullable=True)
    is_imported = Column(Boolean, default=False)

    # Additional Fields
    version = Column(String(50), nullable=True, default="1.0.0")

    is_changes_marketplace = Column(Boolean, nullable=True)

    # Rating and usage tracking
    use_count = Column(Integer, default=0)
    average_rating = Column(Float, default=0.0)

    # Metadata
    visibility = Column(
        Enum(WorkflowVisibilityEnum), nullable=True, default=WorkflowVisibilityEnum.PRIVATE
    )
    category = Column(Enum(WorkflowCategoryEnum), nullable=True)

    tags = Column(JSON, nullable=True)

    status = Column(Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<Workflow {self.name}>"
